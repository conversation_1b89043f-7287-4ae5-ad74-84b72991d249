package com.nutrimedcare.backend.terraweb.vo.resident;

import com.nutrimedcare.backend.terraweb.dao.ResidentInfoEntity;
import com.nutrimedcare.backend.terraweb.enums.medicine.record.MedicationRecordShowStatusEnum;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Period;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
public class ResidentInfoVO {

    private Long id;

    private String residentName;

    private String residentNamePinyin;

    private Integer gender;

    private LocalDateTime birthdate;

    private Integer residentAge;

    private String locationInfo;

    private String originLocationInfo;

    private LocalDateTime resourceUpdateTime;

    /**
     * 服药单状态信息
     *
     * @see MedicationRecordShowStatusEnum
     */
    private Integer medicationRecordStatus;

    /**
     * 存在签名信息
     *
     * @see com.nutrimedcare.backend.terraweb.enums.BooleanEnum
     */
    private Integer signatureRecordShowStatus;

    /**
     * 绑定状态
     */
    private ResidentBoundInfoVO boundInfo;

    public static ResidentInfoVO convert2VO(ResidentInfoEntity entity) {
        if (entity == null) {
            return null;
        }
        ResidentInfoVO vo = new ResidentInfoVO();
        BeanUtils.copyProperties(entity, vo);

        vo.setLocationInfo(entity.getShowLocationInfo());
        vo.setOriginLocationInfo(entity.getLocationInfo());

        if (Objects.nonNull(entity.getBirthdate())) {
            Period between = Period.between(entity.getBirthdate().toLocalDate(), LocalDate.now());
            vo.setResidentAge(between.getYears() + 1);
        }

        return vo;
    }

}