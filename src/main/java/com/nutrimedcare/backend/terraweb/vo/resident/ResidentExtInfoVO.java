package com.nutrimedcare.backend.terraweb.vo.resident;

import com.nutrimedcare.backend.terraweb.dao.resident.ResidentExtInfoEntity;
import com.nutrimedcare.backend.terraweb.enums.resident.ResidentExtInfoTypeEnum;
import lombok.Data;
import org.dromara.hutool.core.collection.CollUtil;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ResidentExtInfoVO {

    private String allergyHistory;

    private String clinicalDiagnosis;

    public static ResidentExtInfoVO convert2VO(List<ResidentExtInfoEntity> extInfoList) {
        if (CollUtil.isEmpty(extInfoList)) {
            return null;
        }

        ResidentExtInfoVO vo = new ResidentExtInfoVO();

        extInfoList.forEach(extInfo -> {
            if (extInfo == null) {
                return;
            }
            if (ResidentExtInfoTypeEnum.ALLERGY_HISTORY.getType().equals(extInfo.getExtType())) {
                vo.setAllergyHistory(extInfo.getExtValue());
            } else if (ResidentExtInfoTypeEnum.CLINICAL_DIAGNOSIS.getType().equals(extInfo.getExtType())) {
                vo.setClinicalDiagnosis(extInfo.getExtValue());
            }
        });

        return vo;
    }

}