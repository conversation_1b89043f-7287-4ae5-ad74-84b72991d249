package com.nutrimedcare.backend.terraweb.vo.medicine.record;

import com.nutrimedcare.backend.terraweb.dto.medicine.MedicineRecordDetailDTO;
import com.nutrimedcare.backend.terraweb.dto.medicine.MedicineSpecificationDTO;
import com.nutrimedcare.backend.terraweb.dto.medicine.MedicineUsageDTO;
import com.nutrimedcare.backend.terraweb.enums.medicine.MedicineUsageTypeEnum;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class MedicationRecordDetailVO {

    /**
     * @see MedicineUsageTypeEnum
     */
    private Integer usageType;

    private String dosageInfo;

    private String dosageUnit;

    /**
     * @see com.nutrimedcare.backend.terraweb.enums.medicine.record.MedicationRecordTimeTypeEnum
     */
    private List<Integer> medicationTimeList;

    /**
     * 实际用法用量信息
     */
    private MedicineUsageDTO usageInfo;

    /**
     * 实际展示规格
     */
    private List<MedicineSpecificationDTO> specificationInfoList;

    private LocalDateTime startTime;

    private LocalDateTime endTime;

    private String remark;

    public static MedicationRecordDetailVO convert2VO(MedicineRecordDetailDTO dto, String dosageInfo, String dosageUnit) {
        MedicationRecordDetailVO vo = new MedicationRecordDetailVO();

        vo.setUsageType(dto.getUsageType());
        vo.setDosageInfo(dosageInfo);
        vo.setDosageUnit(dosageUnit);
        vo.setMedicationTimeList(dto.getMedicationTimeList());
        vo.setUsageInfo(dto.getUsageInfo());
        vo.setSpecificationInfoList(dto.getSpecificationInfoList());

        vo.setStartTime(dto.getStartTime());
        vo.setEndTime(dto.getEndTime());
        vo.setRemark(dto.getRemark());

        return vo;
    }

}