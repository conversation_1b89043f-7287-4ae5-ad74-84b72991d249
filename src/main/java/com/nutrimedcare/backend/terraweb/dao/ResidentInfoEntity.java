package com.nutrimedcare.backend.terraweb.dao;

import com.alibaba.excel.util.StringUtils;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nutrimedcare.backend.terraweb.common.CommonSymbol;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@TableName("tb_resident_info")
public class ResidentInfoEntity {

    @TableId(type = IdType.AUTO)
    private Long id;

    private Long institutionId;

    private String residentName;

    private String residentNamePinyin;

    /*
     * {@link com.nutrimedcare.backend.terraweb.enums.GenderEnum}
     */
    private Integer gender;

    private LocalDateTime birthdate;

    private String locationInfo;

    @TableLogic(value = "0", delval = "unix_timestamp()")
    private Long deleted;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    public String getShowLocationInfo() {
        if (StringUtils.isBlank(this.locationInfo)) {
            return "";
        }
        return this.locationInfo.replace(CommonSymbol.HYPHEN_SYMBOL + CommonSymbol.RESIDENT_LOCATION_PLACEHOLDER, "");
    }

}