package com.nutrimedcare.backend.terraweb.dao.resident;

import com.baomidou.mybatisplus.annotation.TableName;
import com.nutrimedcare.backend.terraweb.dao.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@TableName(value = "tb_resident_ext_info", autoResultMap = true)
public class ResidentExtInfoEntity extends BaseEntity {

    private Long residentId;

    /**
     * @see com.nutrimedcare.backend.terraweb.enums.resident.ResidentExtInfoTypeEnum
     */
    private Integer extType;

    private String extValue;

}