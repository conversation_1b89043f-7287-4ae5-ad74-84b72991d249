package com.nutrimedcare.backend.terraweb.task;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Pair;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nutrimedcare.backend.terraweb.dao.CareInstitutionEntity;
import com.nutrimedcare.backend.terraweb.dao.medicine.MedicationRecordEntity;
import com.nutrimedcare.backend.terraweb.dao.medicine.MedicineInfoEntity;
import com.nutrimedcare.backend.terraweb.dao.medicine.MedicineRunOutSoonLogEntity;
import com.nutrimedcare.backend.terraweb.dao.medicine.MedicineStockEntity;
import com.nutrimedcare.backend.terraweb.dto.medicine.MedicineRecordDetailDTO;
import com.nutrimedcare.backend.terraweb.dto.medicine.MedicineRunOutSoonExtInfoDTO;
import com.nutrimedcare.backend.terraweb.dto.medicine.MedicineUsageDTO;
import com.nutrimedcare.backend.terraweb.enums.medicine.record.MedicationRecordTimeTypeEnum;
import com.nutrimedcare.backend.terraweb.service.CareInstitutionService;
import com.nutrimedcare.backend.terraweb.service.MedicationRecordService;
import com.nutrimedcare.backend.terraweb.service.MedicineInfoService;
import com.nutrimedcare.backend.terraweb.service.MedicineRunOutSoonLogService;
import com.nutrimedcare.backend.terraweb.service.MedicineStockService;
import com.nutrimedcare.backend.terraweb.util.FractionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.dromara.hutool.core.math.Fraction;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @desc 每日统计即将用完的药品
 */
@Slf4j
@Component
public class MedicineRunOutSoonStatTask {

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private CareInstitutionService careInstitutionService;

    @Autowired
    private MedicineInfoService medicineInfoService;

    @Autowired
    private MedicineStockService medicineStockService;

    @Autowired
    private MedicationRecordService medicationRecordService;

    @Autowired
    private MedicineRunOutSoonLogService medicineRunOutSoonLogService;

    @Scheduled(cron = "0 0 3 * * ? ")
    public void task() {
        List<CareInstitutionEntity> allInstitutions = careInstitutionService.list();
        if (CollUtil.isEmpty(allInstitutions)) {
            return;
        }
        allInstitutions.forEach(institution -> dealMedicineStock(institution.getId()));
    }

    private void dealMedicineStock(Long institutionId) {
        int pageNo = 1;
        Page<MedicineStockEntity> page;
        List<MedicineRunOutSoonLogEntity> runOutSoonLogList = new ArrayList<>();
        Set<Pair<Long, String>> medicineSet = new HashSet<>();

        do {
            page = medicineStockService.page(
                    new Page<>(pageNo, 50),
                    new LambdaQueryWrapper<MedicineStockEntity>().eq(MedicineStockEntity::getInstitutionId, institutionId)
            );

            page.getRecords().forEach(stockInfo -> {
                if (medicineSet.contains(Pair.of(stockInfo.getResidentId(), stockInfo.getMedicineName()))) {
                    return;
                }

                // 查询对应的服药单信息，如果该药品存在「不吃」状态，则不统计
                List<MedicationRecordEntity> recordList = medicationRecordService.list(new LambdaQueryWrapper<MedicationRecordEntity>()
                        .eq(MedicationRecordEntity::getResidentId, stockInfo.getResidentId())
                        .eq(MedicationRecordEntity::getMedicineName, stockInfo.getMedicineName())
                );
                if (CollUtil.isEmpty(recordList) || judgeNotEat(recordList.get(0).getUsageDetail())) {
                    return;
                }

                medicineSet.add(Pair.of(stockInfo.getResidentId(), stockInfo.getMedicineName()));
                Fraction stock = FractionUtil.convert2Fraction(stockInfo.getMedicineStock());
                Fraction weekConsumeNum = FractionUtil.convert2Fraction(stockInfo.getWeeklyConsumeNum());
                if (!judgeEnough(stock, weekConsumeNum)) {
                    MedicineRunOutSoonLogEntity runOutSoonLogEntity = convert2MedicineRunOutSoonLogEntity(stockInfo, FractionUtil.convertFromFraction(stock));
                    runOutSoonLogList.add(runOutSoonLogEntity);
                }
            });

            saveMedicineRunOutLog(runOutSoonLogList);
            runOutSoonLogList.clear();

            pageNo += 1;
        } while (page.hasNext());
    }

    /**
     * 当服药单对应所有用法都包含「不吃」选项时，则判断为「不吃」，不统计即将到期
     */
    private boolean judgeNotEat(List<MedicineRecordDetailDTO> usageDetail) {
        if (CollUtil.isEmpty(usageDetail)) {
            return true;
        }
        for (MedicineRecordDetailDTO detail : usageDetail) {
            List<Integer> medicationTimeList = detail.getMedicationTimeList();
            if (CollUtil.isEmpty(medicationTimeList)) {
                return true;
            }
            if (!medicationTimeList.contains(MedicationRecordTimeTypeEnum.NO_EAT.getType())) {
                return false;
            }
        }
        return true;
    }

    private MedicineRunOutSoonLogEntity convert2MedicineRunOutSoonLogEntity(MedicineStockEntity stockEntity, String stock) {
        MedicineInfoEntity medicineInfo = medicineInfoService.getById(stockEntity.getMedicineId());
        if (medicineInfo == null) {
            return null;
        }

        MedicineRunOutSoonLogEntity runOutSoonLog = new MedicineRunOutSoonLogEntity();
        runOutSoonLog.setInstitutionId(medicineInfo.getInstitutionId());
        runOutSoonLog.setResidentName(medicineInfo.getResidentName());
        runOutSoonLog.setResidentNamePinyin(medicineInfo.getResidentNamePinyin());
        runOutSoonLog.setLocationInfo(medicineInfo.getLocationInfo());
        runOutSoonLog.setMedicineName(medicineInfo.getMedicineName());
        runOutSoonLog.setManufacturerInfo(StringUtils.isBlank(medicineInfo.getManufacturerInfo()) ? "/" : medicineInfo.getManufacturerInfo());
        runOutSoonLog.setMedicineStock(stock);
        runOutSoonLog.setExtInfo(buildRunOutSoonExtInfo(stockEntity));
        return runOutSoonLog;
    }

    private MedicineRunOutSoonExtInfoDTO buildRunOutSoonExtInfo(MedicineStockEntity stockEntity) {
        MedicineInfoEntity medicineInfo = medicineInfoService.getById(stockEntity.getMedicineId());
        if (medicineInfo == null) {
            return null;
        }
        MedicineRunOutSoonExtInfoDTO extInfo = new MedicineRunOutSoonExtInfoDTO();
        List<MedicineUsageDTO> usageList;
        List<MedicineUsageDTO> specialUsageList = medicineInfo.getExtInfo().getSpecialUsageList();
        if (CollUtil.isEmpty(specialUsageList)) {
            usageList = Stream.of(Collections.singleton(convert2UsageInfo(medicineInfo.getDosage(), medicineInfo.getUsageInfo()))).flatMap(Collection::stream).toList();
        } else {
            List<MedicineUsageDTO> otherList = specialUsageList.stream().map(usageDTO -> convert2UsageInfo(usageDTO.getDosageInfo(), usageDTO.getUsageType())).toList();
            usageList = Stream.of(Collections.singletonList(convert2UsageInfo(medicineInfo.getDosage(), medicineInfo.getUsageInfo())), otherList).flatMap(Collection::stream).toList();
        }

        extInfo.setUsageList(usageList);
        return extInfo;
    }

    private MedicineUsageDTO convert2UsageInfo(String dosageInfo, Integer usageType) {
        MedicineUsageDTO usageDTO = new MedicineUsageDTO();
        usageDTO.setUsageType(usageType);
        usageDTO.setDosageInfo(dosageInfo);
        return usageDTO;
    }

    private boolean judgeEnough(Fraction stock, Fraction weekConsumeNum) {
        Fraction subtract = stock.subtract(weekConsumeNum);
        String properString = subtract.toProperString();
        return !properString.startsWith("-") && !"0".equals(properString);
    }

    private void saveMedicineRunOutLog(List<MedicineRunOutSoonLogEntity> runOutSoonLogList) {
        List<MedicineRunOutSoonLogEntity> saveList = runOutSoonLogList.stream().filter(Objects::nonNull).toList();
        if (CollUtil.isEmpty(saveList)) {
            return;
        }
        log.info("medicine run out soon stat, saveList:{}", saveList);
        // 删除三天前的记录
        medicineRunOutSoonLogService.remove(new LambdaQueryWrapper<MedicineRunOutSoonLogEntity>()
                .eq(MedicineRunOutSoonLogEntity::getInstitutionId, saveList.get(0).getInstitutionId())
                .lt(MedicineRunOutSoonLogEntity::getCreateTime, LocalDateTime.now().minusDays(3))
        );
        // 保存新纪录
        List<MedicineRunOutSoonLogEntity> filterList = saveList.stream().filter(Objects::nonNull).toList();
        medicineRunOutSoonLogService.saveBatch(filterList);
    }

}
