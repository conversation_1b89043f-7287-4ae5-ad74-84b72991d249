package com.nutrimedcare.backend.terraweb.dto.institution;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class CareInstitutionExtInfoDTO {

    /**
     * 是否存储楼号信息
     */
    private Integer includeBuildingNum;

    /**
     * 床号信息是否必输
     */
    private Integer needBedNum;

    /**
     * 未签名状态下是否可以直接打印药品
     */
    private Integer printWithoutSignature;

    /**
     * 入住人数量限制
     */
    private Integer maxResidentNum = 200;

    /**
     * 服药时间段
     * 比如 1: ["06:00", "07:00"]  表示 [6:00, 7:00)
     * 比如 2: ["07:00", "08:00"]  表示 [7:00, 8:00)
     * List中第一个元素是开始时间，第二个元素是结束时间
     *
     * @see com.nutrimedcare.backend.terraweb.enums.medicine.record.MedicationRecordTimeTypeEnum
     */
    private Map<Integer, List<String>> medicationTakeTimeMap;

}
