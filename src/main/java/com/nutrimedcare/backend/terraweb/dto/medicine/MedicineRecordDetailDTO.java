package com.nutrimedcare.backend.terraweb.dto.medicine;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class MedicineRecordDetailDTO {

    /**
     * @see com.nutrimedcare.backend.terraweb.enums.medicine.MedicineUsageTypeEnum
     */
    private Integer usageType;

    /**
     * 创建服药单时用法用量的快照信息
     */
    private MedicineUsageDTO usageInfoSnap;

    /**
     * @see com.nutrimedcare.backend.terraweb.enums.medicine.record.MedicationRecordTimeTypeEnum
     */
    private List<Integer> medicationTimeList;

    /**
     * 实际用法用量信息
     * - 前端优先展示实际用法用量
     * - 后台任务按照实际用法用量进行扣除
     */
    private MedicineUsageDTO usageInfo;

    /**
     * 实际展示规格
     */
    private List<MedicineSpecificationDTO> specificationInfoList;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 备注
     */
    private String remark;

}
