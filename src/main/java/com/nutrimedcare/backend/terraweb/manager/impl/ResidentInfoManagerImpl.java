package com.nutrimedcare.backend.terraweb.manager.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nutrimedcare.backend.terraweb.common.CommonSymbol;
import com.nutrimedcare.backend.terraweb.dao.CareInstitutionEntity;
import com.nutrimedcare.backend.terraweb.dao.ResidentInfoEntity;
import com.nutrimedcare.backend.terraweb.dao.caregiver.CaregiverInfoEntity;
import com.nutrimedcare.backend.terraweb.dao.caregiver.CaregiverRelatedEntity;
import com.nutrimedcare.backend.terraweb.dao.medicine.MedicationRecordEntity;
import com.nutrimedcare.backend.terraweb.dao.medicine.MedicineInfoEntity;
import com.nutrimedcare.backend.terraweb.dao.medicine.MedicineSignatureEntity;
import com.nutrimedcare.backend.terraweb.dao.resident.ResidentCheckInInfoEntity;
import com.nutrimedcare.backend.terraweb.dao.resident.ResidentNfcRelatedEntity;
import com.nutrimedcare.backend.terraweb.dto.institution.CareInstitutionExtInfoDTO;
import com.nutrimedcare.backend.terraweb.enums.BooleanEnum;
import com.nutrimedcare.backend.terraweb.enums.ResidentListShowSceneTypeEnum;
import com.nutrimedcare.backend.terraweb.enums.medicine.MedicineTypeEnum;
import com.nutrimedcare.backend.terraweb.enums.medicine.record.MedicationRecordShowStatusEnum;
import com.nutrimedcare.backend.terraweb.enums.medicine.signature.MedicineSignatureRoleTypeEnum;
import com.nutrimedcare.backend.terraweb.enums.medicine.signature.MedicineSignatureShowTypeEnum;
import com.nutrimedcare.backend.terraweb.exception.BusinessException;
import com.nutrimedcare.backend.terraweb.manager.ResidentInfoManager;
import com.nutrimedcare.backend.terraweb.manager.medicine.MedicineInfoManager;
import com.nutrimedcare.backend.terraweb.param.IdParam;
import com.nutrimedcare.backend.terraweb.param.resident.ResidentCheckInDataListParam;
import com.nutrimedcare.backend.terraweb.param.resident.ResidentListParam;
import com.nutrimedcare.backend.terraweb.param.resident.ResidentSaveParam;
import com.nutrimedcare.backend.terraweb.service.CareInstitutionService;
import com.nutrimedcare.backend.terraweb.service.MedicationRecordService;
import com.nutrimedcare.backend.terraweb.service.MedicineInfoService;
import com.nutrimedcare.backend.terraweb.service.MedicineSignatureService;
import com.nutrimedcare.backend.terraweb.service.ResidentInfoService;
import com.nutrimedcare.backend.terraweb.service.caregiver.CaregiverInfoService;
import com.nutrimedcare.backend.terraweb.service.caregiver.CaregiverRelatedService;
import com.nutrimedcare.backend.terraweb.service.resident.ResidentCheckInInfoService;
import com.nutrimedcare.backend.terraweb.service.resident.ResidentNfcRelatedService;
import com.nutrimedcare.backend.terraweb.vo.resident.ResidentBoundInfoVO;
import com.nutrimedcare.backend.terraweb.vo.resident.ResidentCheckIntInfoVO;
import com.nutrimedcare.backend.terraweb.vo.resident.ResidentInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class ResidentInfoManagerImpl implements ResidentInfoManager {

    @Autowired
    private CareInstitutionService careInstitutionService;

    @Autowired
    private ResidentInfoService residentInfoService;

    @Autowired
    private MedicineInfoService medicineInfoService;

    @Autowired
    private MedicationRecordService medicationRecordService;

    @Autowired
    private MedicineSignatureService medicineSignatureService;

    @Autowired
    private MedicineInfoManager medicineInfoManager;

    @Autowired
    private CaregiverInfoService caregiverInfoService;

    @Autowired
    private CaregiverRelatedService caregiverRelatedService;

    @Autowired
    private ResidentNfcRelatedService residentNfcRelatedService;

    @Autowired
    private ResidentCheckInInfoService residentCheckInInfoService;

    @Override
    public List<ResidentInfoVO> list(ResidentListParam param) {
        if (param == null) {
            return Collections.emptyList();
        }

        List<ResidentInfoEntity> entityList = residentInfoService.list(new LambdaQueryWrapper<ResidentInfoEntity>()
                .eq(ResidentInfoEntity::getInstitutionId, param.getInstitutionId())
                .like(param.getResidentName() != null, ResidentInfoEntity::getResidentName, param.getResidentName())
        );

        // build vo list
        List<ResidentInfoVO> voList = entityList.stream().map(ResidentInfoVO::convert2VO).toList();

        fillResourceUpdateTime(param.getShowScene(), voList);

        fillMedicationRecordInfo(param.getShowScene(), voList);
        fillSignatureRecordInfo(param.getShowScene(), voList);

        return voList;
    }

    private void fillResourceUpdateTime(Integer showScene, List<ResidentInfoVO> voList) {
        if (CollectionUtils.isEmpty(voList)) {
            return;
        }
        Set<Long> residentIds = voList.stream().map(ResidentInfoVO::getId).collect(Collectors.toSet());
        Map<Long, LocalDateTime> latestUpdateTimeMap = getLatestUpdateTimesMap(showScene, residentIds);
        voList.forEach(vo -> vo.setResourceUpdateTime(latestUpdateTimeMap.get(vo.getId())));
    }

    private Map<Long, LocalDateTime> getLatestUpdateTimesMap(Integer showScene, Set<Long> residentIds) {
        if (ResidentListShowSceneTypeEnum.MEDICINE_MANAGER.getType().equals(showScene)) {
            return medicineInfoService.getLatestUpdateTimesMap(residentIds);
        } else if (ResidentListShowSceneTypeEnum.MEDICINE_RECORD.getType().equals(showScene)) {
            return medicationRecordService.getLatestUpdateTimesMap(residentIds);
        } else {
            return medicineInfoService.getLatestUpdateTimesMap(residentIds);
        }
    }

    private void fillMedicationRecordInfo(Integer showScene, List<ResidentInfoVO> voList) {
        if (CollectionUtils.isEmpty(voList) || !ResidentListShowSceneTypeEnum.MEDICINE_RECORD.getType().equals(showScene)) {
            return;
        }

        // 获取入住人 id
        List<Long> residentIds = voList.stream().map(ResidentInfoVO::getId).toList();
        Map<Long, List<MedicineInfoEntity>> resident2MedicineMap = medicineInfoManager.listSignedMedicineInfoByResidentId(residentIds);

        // 查询入住人服药记录
        List<MedicationRecordEntity> recordList = medicationRecordService.list(new LambdaQueryWrapper<MedicationRecordEntity>()
                .in(MedicationRecordEntity::getResidentId, residentIds)
        );
        Map<Long, List<MedicationRecordEntity>> recordMap = recordList.stream().collect(Collectors.groupingBy(MedicationRecordEntity::getResidentId, Collectors.toList()));

        voList.forEach(vo -> {
            List<MedicationRecordEntity> matchedRecordList = recordMap.getOrDefault(vo.getId(), Collections.emptyList());
            if (CollUtil.isEmpty(matchedRecordList)) {
                vo.setMedicationRecordStatus(MedicationRecordShowStatusEnum.NO_RECORD.getType());
                return;
            }
            Set<String> existMedicineNameInRecord = matchedRecordList.stream().map(MedicationRecordEntity::getMedicineName).collect(Collectors.toSet());

            List<MedicineInfoEntity> medicineInfoList = resident2MedicineMap.getOrDefault(vo.getId(), Collections.emptyList());
            Set<String> signedMedicineNameSet = medicineInfoList.stream().map(MedicineInfoEntity::getMedicineName).collect(Collectors.toSet());
            if (!existMedicineNameInRecord.containsAll(signedMedicineNameSet)) {
                vo.setMedicationRecordStatus(MedicationRecordShowStatusEnum.HAS_UPDATED.getType());
                return;
            }


            List<MedicationRecordEntity> filterList = matchedRecordList.stream()
                    .filter(recordEntity -> !BooleanEnum.TRUE.getType().equals(recordEntity.getValidStatus()))
                    .toList();
            vo.setMedicationRecordStatus(CollUtil.isEmpty(filterList)
                    ? MedicationRecordShowStatusEnum.HAVE_RECORD.getType() : MedicationRecordShowStatusEnum.HAS_UPDATED.getType()
            );
        });
    }

    private void fillSignatureRecordInfo(Integer showScene, List<ResidentInfoVO> voList) {
        if (CollectionUtils.isEmpty(voList) || !ResidentListShowSceneTypeEnum.MEDICINE_MANAGER.getType().equals(showScene)) {
            return;
        }

        Set<Long> residentIds = voList.stream().map(ResidentInfoVO::getId).collect(Collectors.toSet());

        Map<Long, List<MedicineInfoEntity>> resident2MedicinesMap = medicineInfoService.list(
                        new LambdaQueryWrapper<MedicineInfoEntity>()
                                .in(MedicineInfoEntity::getResidentId, residentIds)
                                .select(MedicineInfoEntity::getId, MedicineInfoEntity::getResidentId, MedicineInfoEntity::getMedicineType)
                ).stream()
                .collect(Collectors.groupingBy(
                        MedicineInfoEntity::getResidentId,
                        Collectors.mapping(Function.identity(), Collectors.toList())
                ));

        Map<Long, List<MedicineSignatureEntity>> resident2SignaturesMap = medicineSignatureService.list(
                        new LambdaQueryWrapper<MedicineSignatureEntity>()
                                .in(MedicineSignatureEntity::getResidentId, residentIds)
                                .select(MedicineSignatureEntity::getResidentId, MedicineSignatureEntity::getMedicineId, MedicineSignatureEntity::getRoleType)
                ).stream()
                .collect(Collectors.groupingBy(
                        MedicineSignatureEntity::getResidentId,
                        Collectors.mapping(Function.identity(), Collectors.toList())
                ));

        voList.forEach(vo -> {
            List<MedicineInfoEntity> allMedicine = resident2MedicinesMap.getOrDefault(vo.getId(), Collections.emptyList());
            if (CollectionUtils.isEmpty(allMedicine)) {
                vo.setSignatureRecordShowStatus(MedicationRecordShowStatusEnum.NO_RECORD.getType());
                return;
            }

            List<MedicineSignatureEntity> allSignature = resident2SignaturesMap.getOrDefault(vo.getId(), Collections.emptyList());
            Map<Long, Set<Integer>> medicineIdToRoleTypes = allSignature.stream()
                    .collect(Collectors.groupingBy(
                            MedicineSignatureEntity::getMedicineId,
                            Collectors.mapping(MedicineSignatureEntity::getRoleType, Collectors.toSet())
                    ));

            boolean hasNoSignature = allMedicine.stream().anyMatch(medicine -> {
                Set<Integer> currentRoleTypes = medicineIdToRoleTypes.get(medicine.getId());
                if (CollectionUtils.isEmpty(currentRoleTypes)) {
                    return true;
                }
                if (MedicineTypeEnum.DISPENSING_OF_MEDICINES.getType().equals(medicine.getMedicineType())) {
                    return !currentRoleTypes.containsAll(Arrays.asList(
                            MedicineSignatureRoleTypeEnum.MANAGER.getType(),
                            MedicineSignatureRoleTypeEnum.PHARMACY.getType()
                    ));
                }
                return !currentRoleTypes.containsAll(Arrays.asList(
                        MedicineSignatureRoleTypeEnum.FAMILY_MEMBER.getType(),
                        MedicineSignatureRoleTypeEnum.MANAGER.getType(),
                        MedicineSignatureRoleTypeEnum.PHARMACY.getType()
                ));
            });
            vo.setSignatureRecordShowStatus(hasNoSignature ? MedicineSignatureShowTypeEnum.NO_SIGNATURE.getType() : MedicineSignatureShowTypeEnum.HAVE_SIGNATURE.getType());
        });
    }

    @Override
    public List<ResidentInfoVO> simpleList(ResidentListParam param) {
        if (param == null) {
            return Collections.emptyList();
        }

        List<ResidentInfoVO> voList = listResidentByCondition(param);
        fillBoundStatus(voList);

        return voList;
    }

    private List<ResidentInfoVO> listResidentByCondition(ResidentListParam param) {
        if (StringUtils.isBlank(param.getSearchCondition())) {
            // 没有搜索条件，返回所有入住人
            return listResidentInfoByCondition(param.getInstitutionId(), null, null);
        }

        // 条件作为姓名进行模糊查询
        List<ResidentInfoVO> nameLikeList = listResidentInfoByCondition(param.getInstitutionId(), param.getSearchCondition(), null);
        if (CollUtil.isNotEmpty(nameLikeList)) {
            return nameLikeList;
        }
        // 条件作为位置信息进行模糊查询
        List<ResidentInfoVO> locationLikeList = listResidentInfoByCondition(param.getInstitutionId(), null, param.getSearchCondition());
        if (CollUtil.isNotEmpty(locationLikeList)) {
            return locationLikeList;
        }

        return Collections.emptyList();
    }

    private void fillBoundStatus(List<ResidentInfoVO> voList) {
        List<Long> residentIds = voList.stream().map(ResidentInfoVO::getId).toList();
        if (CollUtil.isEmpty(residentIds)) {
            return;
        }
        List<CaregiverRelatedEntity> relationList = caregiverRelatedService.list(new LambdaQueryWrapper<CaregiverRelatedEntity>()
                .in(CaregiverRelatedEntity::getResidentId, residentIds)
        );

        Map<Long, Long> boundResidentMap = Optional.of(relationList).orElse(Collections.emptyList())
                .stream().collect(Collectors.toMap(CaregiverRelatedEntity::getResidentId, CaregiverRelatedEntity::getCaregiverId, (v1, v2) -> v2));
        Map<Long, String> caregiver2DisplayIdMap = findCaregiverDisplayIdMap(relationList);

        voList.forEach(vo -> {
            Long residentId = vo.getId();
            Long caregiverId = boundResidentMap.get(residentId);
            ResidentBoundInfoVO boundInfoVO = ResidentBoundInfoVO.builder()
                    .boundStatus(boundResidentMap.containsKey(residentId) ? BooleanEnum.TRUE.getType() : BooleanEnum.FALSE.getType())
                    .caregiverDisplayId(caregiver2DisplayIdMap.get(caregiverId)).build();
            vo.setBoundInfo(boundInfoVO);
        });
    }

    private Map<Long, String> findCaregiverDisplayIdMap(List<CaregiverRelatedEntity> relationList) {
        List<Long> caregiverIds = relationList.stream().map(CaregiverRelatedEntity::getCaregiverId).toList();
        if (CollUtil.isEmpty(caregiverIds)) {
            return Collections.emptyMap();
        }
        List<CaregiverInfoEntity> caregiverList = caregiverInfoService.list(new LambdaQueryWrapper<CaregiverInfoEntity>()
                .in(CaregiverInfoEntity::getId, caregiverIds)
        );
        return caregiverList.stream().collect(Collectors.toMap(CaregiverInfoEntity::getId, CaregiverInfoEntity::getDisplayId, (v1, v2) -> v2));
    }

    private List<ResidentInfoVO> listResidentInfoByCondition(Long institutionId, String residentName, String locationInfo) {
        if (institutionId == null) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ResidentInfoEntity> wrapper = new LambdaQueryWrapper<ResidentInfoEntity>()
                .eq(ResidentInfoEntity::getInstitutionId, institutionId)
                .orderByAsc(ResidentInfoEntity::getResidentNamePinyin)
                .select(ResidentInfoEntity::getId, ResidentInfoEntity::getBirthdate, ResidentInfoEntity::getGender, ResidentInfoEntity::getResidentName, ResidentInfoEntity::getResidentNamePinyin, ResidentInfoEntity::getLocationInfo);
        if (StringUtils.isNotBlank(residentName)) {
            wrapper.like(ResidentInfoEntity::getResidentName, residentName);
        }
        if (StringUtils.isNotBlank(locationInfo)) {
            wrapper.like(ResidentInfoEntity::getLocationInfo, locationInfo);
        }

        List<ResidentInfoEntity> list = residentInfoService.list(wrapper);
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.stream().map(ResidentInfoVO::convert2VO).toList();
    }


    @Override
    public List<ResidentInfoVO> searchRes(ResidentListParam param) {
        if (param == null || StringUtils.isBlank(param.getResidentName())) {
            return Collections.emptyList();
        }

        List<ResidentInfoEntity> list = residentInfoService.list(
                new LambdaQueryWrapper<ResidentInfoEntity>()
                        .eq(ResidentInfoEntity::getInstitutionId, param.getInstitutionId())
                        .like(ResidentInfoEntity::getResidentName, param.getResidentName())
                        .orderByAsc(ResidentInfoEntity::getResidentNamePinyin)
                        .select(ResidentInfoEntity::getId, ResidentInfoEntity::getResidentName)
        );

        return list.stream().map(ResidentInfoVO::convert2VO).toList();
    }

    @Override
    public ResidentInfoVO detail(Long id, Integer needMedicationRecord) {
        if (id == null || needMedicationRecord == null) {
            return null;
        }

        // find resident detail
        ResidentInfoEntity entity = residentInfoService.getById(id);
        if (entity == null) {
            throw new BusinessException("未查询到入住者信息");
        }

        // find resident's latest update time(medicine list or medicine record time)
        Map<Long, LocalDateTime> latestUpdateTimeMap = getLatestUpdateTimesMap(needMedicationRecord, Collections.singleton(id));

        // build vo
        ResidentInfoVO vo = ResidentInfoVO.convert2VO(entity);
        vo.setResourceUpdateTime(latestUpdateTimeMap.get(id));

        return vo;
    }

    @Override
    public ResidentInfoVO save(ResidentSaveParam param) {
        // param trim
        param.trimFields();
        // check institution
        CareInstitutionEntity careInstitution = careInstitutionService.findByIdAndCheckNull(param.getInstitutionId());
        // checkMaxResidentNum
        checkMaxResidentNum(param.getId(), param.getInstitutionId());
        // check location info
        checkLocationInfo(careInstitution.getExtInfo(), param);
        // check resident location unique
        checkResidentLocationUnique(param.getInstitutionId(), param.getId(), parseLocationInfo(careInstitution.getExtInfo(), param));

        // build entity
        ResidentInfoEntity resident = new ResidentInfoEntity();
        BeanUtils.copyProperties(param, resident);
        resident.setResidentNamePinyin(PinyinUtil.getPinyin(param.getResidentName()));
        resident.setLocationInfo(parseLocationInfo(careInstitution.getExtInfo(), param));

        if (Objects.isNull(param.getId())) {
            residentInfoService.save(resident);
        } else {
            residentInfoService.updateById(resident);
            updateResidentInfoInMedicine(resident);
        }

        return ResidentInfoVO.convert2VO(resident);
    }

    private void checkLocationInfo(CareInstitutionExtInfoDTO extInfo, ResidentSaveParam param) {
        // 如果 extInfo 为空，则表示机构信息未配置，需要检查楼号和床号是否都填写
        if (extInfo == null) {
            if (Objects.isNull(param.getBedNum())) {
                throw new BusinessException("长者床号信息必填");
            }
        } else {
            if (BooleanEnum.TRUE.getType().equals(extInfo.getIncludeBuildingNum())
                    && Objects.isNull(param.getBuildingNum())) {
                throw new BusinessException("长者楼号信息必填");
            }
            if ((!BooleanEnum.FALSE.getType().equals(extInfo.getNeedBedNum()))
                    && Objects.isNull(param.getBedNum())) {
                throw new BusinessException("长者床号信息必填");
            }
        }
    }

    private String parseLocationInfo(CareInstitutionExtInfoDTO extInfo, ResidentSaveParam param) {
        if (param == null) {
            return "";
        }
        if (extInfo != null && BooleanEnum.TRUE.getType().equals(extInfo.getIncludeBuildingNum())) {
            return StringUtils.joinWith(CommonSymbol.HYPHEN_SYMBOL, param.getBuildingNum(), linkRoomAndBedNum(param));
        }
        return linkRoomAndBedNum(param);
    }

    private String linkRoomAndBedNum(ResidentSaveParam param) {
        String bedNum = param.getBedNum();
        if (StringUtils.isBlank(bedNum)) {
            return StringUtils.joinWith(CommonSymbol.HYPHEN_SYMBOL, param.getRoomNum(), CommonSymbol.RESIDENT_LOCATION_PLACEHOLDER);
        }
        return StringUtils.joinWith(CommonSymbol.HYPHEN_SYMBOL, param.getRoomNum(), bedNum);
    }

    private void checkMaxResidentNum(Long residentId, Long institutionId) {
        if (residentId != null) {
            return;
        }
        CareInstitutionEntity institution = careInstitutionService.findByIdAndCheckNull(institutionId);
        Integer maxResidentNum = Optional.ofNullable(institution.getExtInfo()).orElse(new CareInstitutionExtInfoDTO()).getMaxResidentNum();
        long count = residentInfoService.count(new LambdaQueryWrapper<ResidentInfoEntity>().eq(ResidentInfoEntity::getInstitutionId, institutionId));
        if (count > maxResidentNum - 1) {
            throw new BusinessException("超过入住者数量限制，请联系机构管理员");
        }
    }

    private void checkResidentLocationUnique(Long institutionId, Long residentId, String locationInfo) {
        if (institutionId == null || StringUtils.isBlank(locationInfo)) {
            return;
        }
        List<ResidentInfoEntity> list = residentInfoService.list(new LambdaQueryWrapper<ResidentInfoEntity>()
                .eq(ResidentInfoEntity::getInstitutionId, institutionId)
                .eq(ResidentInfoEntity::getLocationInfo, locationInfo)
        );

        if (CollUtil.isEmpty(list)) {
            return;
        }

        ResidentInfoEntity residentInfo = list.get(0);
        if (residentId == null || !Objects.equals(residentInfo.getId(), residentId)) {
            throw new BusinessException("该床号已有入住者");
        }

    }

    private void updateResidentInfoInMedicine(ResidentInfoEntity residentInfo) {
        if (residentInfo == null) {
            return;
        }
        List<MedicineInfoEntity> list = medicineInfoService.list(
                new LambdaQueryWrapper<MedicineInfoEntity>()
                        .eq(MedicineInfoEntity::getResidentId, residentInfo.getId())
        );
        List<MedicineInfoEntity> updateList = list.stream().map(entity -> {
            MedicineInfoEntity updateEntity = new MedicineInfoEntity();
            updateEntity.setId(entity.getId());
            updateEntity.setResidentName(residentInfo.getResidentName());
            updateEntity.setResidentNamePinyin(residentInfo.getResidentNamePinyin());
            updateEntity.setLocationInfo(residentInfo.getShowLocationInfo());
            return updateEntity;
        }).toList();
        medicineInfoService.updateBatchById(updateList);
    }

    @Override
    public Boolean delete(IdParam param) {
        if (param == null || param.getId() == null) {
            return false;
        }

        // remove resident
        boolean res = residentInfoService.removeById(param.getId());
        if (!res) {
            log.warn("resident delete, fail to delete resident, id: {}", param.getId());
            throw new BusinessException("入住人删除失败");
        }

        // remove resident's medicine list
        long count = medicineInfoService.count(new LambdaQueryWrapper<MedicineInfoEntity>().eq(MedicineInfoEntity::getResidentId, param.getId()));
        if (count == 0) {
            return true;
        }
        boolean removeRes = medicineInfoService.remove(
                new LambdaQueryWrapper<MedicineInfoEntity>().eq(MedicineInfoEntity::getResidentId, param.getId())
        );
        if (!removeRes) {
            log.warn("resident delete, fail to delete resident's medicine list, id: {}", param.getId());
            throw new BusinessException("入住人药物列表删除失败");
        }

        return true;
    }

    @Override
    public List<ResidentInfoVO> list4EaseTab(ResidentListParam param) {
        if (param == null) {
            return Collections.emptyList();
        }

        List<ResidentInfoVO> voList;
        // 通过入住人信息查询
        voList = listResidentByCondition(param);
        // 通过 NFC 卡片信息查询
        if (CollUtil.isEmpty(voList)) {
            voList = listResidentByNfcUniqueId(param);
        }

        fillBoundStatus(voList);
        fillBoundNfcStatus(voList);

        return voList;
    }

    private List<ResidentInfoVO> listResidentByNfcUniqueId(ResidentListParam param) {
        List<ResidentNfcRelatedEntity> list = residentNfcRelatedService.list(new LambdaQueryWrapper<ResidentNfcRelatedEntity>()
                .eq(ResidentNfcRelatedEntity::getInstitutionId, param.getInstitutionId())
                .like(ResidentNfcRelatedEntity::getNfcUniqueId, param.getSearchCondition())
        );
        List<Long> residentIds = list.stream().map(ResidentNfcRelatedEntity::getResidentId).toList();
        if (CollUtil.isEmpty(residentIds)) {
            return Collections.emptyList();
        }
        List<ResidentInfoEntity> residentInfoList = residentInfoService.list(new LambdaQueryWrapper<ResidentInfoEntity>()
                .in(ResidentInfoEntity::getId, residentIds)
        );
        if (CollUtil.isEmpty(residentInfoList)) {
            return Collections.emptyList();
        }
        return residentInfoList.stream().map(ResidentInfoVO::convert2VO).toList();
    }

    private void fillBoundNfcStatus(List<ResidentInfoVO> voList) {
        List<Long> residentIds = voList.stream().map(ResidentInfoVO::getId).toList();
        if (CollUtil.isEmpty(residentIds)) {
            return;
        }
        List<ResidentNfcRelatedEntity> relationList = residentNfcRelatedService.list(new LambdaQueryWrapper<ResidentNfcRelatedEntity>()
                .in(ResidentNfcRelatedEntity::getResidentId, residentIds)
        );
        Map<Long, String> boundResidentMap = Optional.of(relationList).orElse(Collections.emptyList())
                .stream().collect(Collectors.toMap(ResidentNfcRelatedEntity::getResidentId, ResidentNfcRelatedEntity::getNfcUniqueId, (v1, v2) -> v2));

        voList.forEach(vo -> {
            if (vo.getBoundInfo() == null) {
                ResidentBoundInfoVO boundInfoVO = ResidentBoundInfoVO.builder()
                        .boundNfcStatus(boundResidentMap.containsKey(vo.getId()) ? BooleanEnum.TRUE.getType() : BooleanEnum.FALSE.getType())
                        .boundNfcUniqueId(boundResidentMap.get(vo.getId()))
                        .build();
                vo.setBoundInfo(boundInfoVO);
                return;
            }
            ResidentBoundInfoVO boundInfo = vo.getBoundInfo();
            boundInfo.setBoundNfcStatus(boundResidentMap.containsKey(vo.getId()) ? BooleanEnum.TRUE.getType() : BooleanEnum.FALSE.getType());
            vo.getBoundInfo().setBoundNfcUniqueId(boundResidentMap.get(vo.getId()));
        });
    }

    @Override
    public ResidentInfoVO detail4Ease(Long id) {
        if (id == null) {
            return null;
        }

        ResidentInfoEntity entity = residentInfoService.findByIdAndCheckNull(id);
        ResidentInfoVO vo = ResidentInfoVO.convert2VO(entity);

        fillBoundStatus(Collections.singletonList(vo));
        fillBoundNfcStatus(Collections.singletonList(vo));

        return vo;
    }

    @Override
    public List<ResidentCheckIntInfoVO> listCheckInInfo(ResidentCheckInDataListParam param) {
        if (param == null || param.getInstitutionId() == null) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<ResidentCheckInInfoEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(param.getLocationInfo() != null, ResidentCheckInInfoEntity::getLocationInfo, param.getLocationInfo())
                .like(param.getResidentName() != null, ResidentCheckInInfoEntity::getResidentName, param.getResidentName())
                .like(param.getMedicineName() != null, ResidentCheckInInfoEntity::getMedicineName, param.getMedicineName())
                .like(param.getCaregiverDisplayId() != null, ResidentCheckInInfoEntity::getCaregiverDisplayId, param.getCaregiverDisplayId())
                .eq(param.getTakeStatus() != null, ResidentCheckInInfoEntity::getMedicineTakeStatus, param.getTakeStatus())
                .eq(ResidentCheckInInfoEntity::getInstitutionId, param.getInstitutionId())
                .orderByDesc(ResidentCheckInInfoEntity::getCheckInTime);

        fillCheckInTimeCondition(param, wrapper);

        List<ResidentCheckInInfoEntity> list = residentCheckInInfoService.page(new Page<>(param.getPageNo(), param.getPageSize()), wrapper).getRecords();

        return list.stream().map(ResidentCheckIntInfoVO::convert2VO).toList();
    }

    private void fillCheckInTimeCondition(ResidentCheckInDataListParam param, LambdaQueryWrapper<ResidentCheckInInfoEntity> wrapper) {
        if (param.getCheckInDate() == null) {
            return;
        }

        LocalDate checkInTime = param.getCheckInDate();
        LocalDateTime startOfDay = checkInTime.atStartOfDay();
        LocalDateTime endOfDay = LocalDateTimeUtil.endOfDay(startOfDay, true);

        wrapper.between(ResidentCheckInInfoEntity::getCheckInTime, startOfDay, endOfDay);

    }

}