package com.nutrimedcare.backend.terraweb.manager.medicine;

import com.nutrimedcare.backend.terraweb.param.medicine.record.MedicationRecordCreateParam;
import com.nutrimedcare.backend.terraweb.param.medicine.record.MedicationRecordExtInfoSaveParam;
import com.nutrimedcare.backend.terraweb.param.medicine.record.MedicineRecordQueryParam;
import com.nutrimedcare.backend.terraweb.vo.medicine.record.MedicationRecordListVO;
import jakarta.validation.Valid;

import java.util.Set;

/**
 * <AUTHOR>
 */
public interface MedicineRecordManager {

    Boolean save(MedicationRecordCreateParam param);

    void calMedicineStock(Long residentId, Set<String> medicineNames);

    MedicationRecordListVO list(MedicineRecordQueryParam param);

    Boolean saveExtInfo(MedicationRecordExtInfoSaveParam param);

}