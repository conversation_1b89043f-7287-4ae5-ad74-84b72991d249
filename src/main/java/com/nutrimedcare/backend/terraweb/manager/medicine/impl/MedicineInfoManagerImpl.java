package com.nutrimedcare.backend.terraweb.manager.medicine.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.Pair;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nutrimedcare.backend.terraweb.dao.ResidentInfoEntity;
import com.nutrimedcare.backend.terraweb.dao.medicine.MedicationRecordEntity;
import com.nutrimedcare.backend.terraweb.dao.medicine.MedicineInfoEntity;
import com.nutrimedcare.backend.terraweb.dao.medicine.MedicineRunOutSoonLogEntity;
import com.nutrimedcare.backend.terraweb.dao.medicine.MedicineSignatureEntity;
import com.nutrimedcare.backend.terraweb.dao.medicine.MedicineStockEntity;
import com.nutrimedcare.backend.terraweb.dto.medicine.MedicineExtInfoDTO;
import com.nutrimedcare.backend.terraweb.enums.BooleanEnum;
import com.nutrimedcare.backend.terraweb.enums.medicine.MedicineExpirationTypeEnum;
import com.nutrimedcare.backend.terraweb.enums.medicine.MedicineGroupStrategyEnum;
import com.nutrimedcare.backend.terraweb.enums.medicine.MedicineSortTypeEnum;
import com.nutrimedcare.backend.terraweb.enums.medicine.MedicineTypeEnum;
import com.nutrimedcare.backend.terraweb.enums.medicine.signature.MedicineSignatureRoleTypeEnum;
import com.nutrimedcare.backend.terraweb.exception.BusinessException;
import com.nutrimedcare.backend.terraweb.manager.medicine.MedicineInfoManager;
import com.nutrimedcare.backend.terraweb.param.medicine.MedicineInfoBatchParam;
import com.nutrimedcare.backend.terraweb.param.medicine.MedicineInfoList4ResidentParam;
import com.nutrimedcare.backend.terraweb.param.medicine.MedicineInfoListParam;
import com.nutrimedcare.backend.terraweb.param.medicine.MedicineInfoSaveParam;
import com.nutrimedcare.backend.terraweb.param.medicine.MedicineInfoSortConditionParam;
import com.nutrimedcare.backend.terraweb.param.medicine.MedicineListParam;
import com.nutrimedcare.backend.terraweb.param.medicine.MedicineSaveParam;
import com.nutrimedcare.backend.terraweb.service.MedicationRecordService;
import com.nutrimedcare.backend.terraweb.service.MedicineInfoService;
import com.nutrimedcare.backend.terraweb.service.MedicineRunOutSoonLogService;
import com.nutrimedcare.backend.terraweb.service.MedicineSignatureService;
import com.nutrimedcare.backend.terraweb.service.MedicineStockService;
import com.nutrimedcare.backend.terraweb.service.ResidentInfoService;
import com.nutrimedcare.backend.terraweb.util.FractionUtil;
import com.nutrimedcare.backend.terraweb.util.MedicineUtil;
import com.nutrimedcare.backend.terraweb.vo.medicine.MedicineInfoSaveVO;
import com.nutrimedcare.backend.terraweb.vo.medicine.MedicineInfoVO;
import com.nutrimedcare.backend.terraweb.vo.medicine.MedicineListVO;
import com.nutrimedcare.backend.terraweb.vo.medicine.MedicineSignatureVO;
import lombok.extern.slf4j.Slf4j;
import org.dromara.hutool.core.math.Fraction;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class MedicineInfoManagerImpl implements MedicineInfoManager {

    @Autowired
    private MedicineUtil medicineUtil;

    @Autowired
    private ResidentInfoService residentInfoService;

    @Autowired
    private MedicineInfoService medicineInfoService;

    @Autowired
    private MedicationRecordService medicationRecordService;

    @Autowired
    private MedicineSignatureService medicineSignatureService;

    @Autowired
    private MedicineRunOutSoonLogService medicineRunOutSoonLogService;

    @Autowired
    private MedicineStockService medicineStockService;

    @Override
    public MedicineInfoSaveVO save(MedicineSaveParam param) {
        if (param == null || CollUtil.isEmpty(param.getInfoList())) {
            return MedicineInfoSaveVO.builder().build();
        }

        List<MedicineInfoSaveParam> infoList = param.getInfoList();
        LocalDateTime now = LocalDateTime.now();

        // check param
        checkBaseParam(param.getMedicineType(), infoList);
        // check resident exist
        ResidentInfoEntity residentInfo = residentInfoService.findByIdAndCheckNull(param.getInfoList().get(0).getResidentId());

        // build medicine list
        List<MedicineInfoEntity> medicineInfoEntityList = infoList.stream()
                .map(saveParam -> generateMedicneList(saveParam, param, residentInfo, now))
                .flatMap(Collection::stream).toList();
        Pair<List<MedicineInfoEntity>, List<MedicineInfoEntity>> pair = distinguishRepeat(medicineInfoEntityList);
        if (CollUtil.isNotEmpty(pair.getValue())) {
            log.warn("medicine save, today's medicine code pool has been exhausted，unsaved list: {}", pair.getValue());
        }

        // save medicine info
        boolean saveMedicineInfoRes = medicineInfoService.saveBatch(pair.getKey());
        if (!saveMedicineInfoRes) {
            log.warn("medicine save, fail to save medicine info, param:{}", param);
            return MedicineInfoSaveVO.builder().build();
        }

        return MedicineInfoSaveVO.builder()
                .medicineIds(Optional.ofNullable(pair.getKey()).orElse(Collections.emptyList()).stream().map(MedicineInfoEntity::getId).toList())
                .medicineInfoList(Optional.ofNullable(pair.getKey()).orElse(Collections.emptyList()).stream().map(MedicineInfoVO::convert2VO).toList())
                .build();
    }

    private void checkBaseParam(Integer medicineType, List<MedicineInfoSaveParam> infoList) {
        // 代配药不需要登记药厂、批号、有效期
        if (MedicineTypeEnum.DISPENSING_OF_MEDICINES.getType().equals(medicineType)) {
            return;
        }
        boolean matchRes = infoList.stream().anyMatch(medicineInfoSaveParam ->
                medicineInfoSaveParam.getManufacturerInfo() == null
                        || medicineInfoSaveParam.getBatchNumber() == null
                        || medicineInfoSaveParam.getExpirationTime() == null
        );
        if (matchRes) {
            throw new BusinessException("请检查药厂、批号、有效期信息是否填写完整");
        }
    }

    private List<MedicineInfoEntity> generateMedicneList(MedicineInfoSaveParam saveParam, MedicineSaveParam param,
                                                         ResidentInfoEntity residentInfo, LocalDateTime now) {
        return IntStream.range(0, saveParam.getMedicineQuantity())
                .mapToObj(i -> {
                    MedicineInfoEntity medicine = new MedicineInfoEntity();
                    BeanUtils.copyProperties(saveParam, medicine);
                    medicine.setMedicineProperty(param.getMedicineProperty());
                    medicine.setMedicineType(param.getMedicineType());
                    medicine.setRegisterTime(LocalDateTime.now());
                    medicine.setMedicineQuantity(1);
                    medicine.setMedicineIdentifier(LocalDateTimeUtil.format(now, DateTimeFormatter.ofPattern("yyyyMMddHH")) + "_" + medicine.getMedicineName());
                    medicine.setExtInfo(updateMedicineExt(saveParam));
                    fillMedicineCode(medicine, now);
                    fillResidentInfo(medicine, residentInfo);
                    return medicine;
                })
                .toList();
    }

    private MedicineExtInfoDTO updateMedicineExt(MedicineInfoSaveParam saveParam) {
        MedicineExtInfoDTO extInfo = saveParam.getExtInfo();
        extInfo.setInitNum(saveParam.getMedicineContentPer());
        extInfo.setStock(convert2Stock(saveParam.getMedicineContentPer()));
        return extInfo;
    }

    private String convert2Stock(Integer contentPer) {
        if (contentPer == null) {
            return "0-0-0";
        }
        return contentPer + "-0-0";
    }

    private void fillMedicineCode(MedicineInfoEntity medicine, LocalDateTime now) {
        medicine.setMedicineCode(medicineUtil.generateMedicineCode(now));
    }

    private void fillResidentInfo(MedicineInfoEntity medicine, ResidentInfoEntity residentInfo) {
        medicine.setInstitutionId(residentInfo.getInstitutionId());
        medicine.setResidentId(residentInfo.getId());
        medicine.setResidentName(residentInfo.getResidentName());
        medicine.setResidentNamePinyin(residentInfo.getResidentNamePinyin());
        medicine.setLocationInfo(residentInfo.getShowLocationInfo());
    }

    private Pair<List<MedicineInfoEntity>, List<MedicineInfoEntity>> distinguishRepeat(List<MedicineInfoEntity> medicineInfoList) {
        if (CollUtil.isEmpty(medicineInfoList)) {
            return Pair.of(Collections.emptyList(), Collections.emptyList());
        }

        // list repeat code
        List<String> generateCodeList = medicineInfoList.stream().map(MedicineInfoEntity::getMedicineCode).toList();
        List<MedicineInfoEntity> repeatList = medicineInfoService.list(
                new LambdaQueryWrapper<MedicineInfoEntity>().in(MedicineInfoEntity::getMedicineCode, generateCodeList)
        );
        Set<String> repeatCodeList = repeatList.stream().map(MedicineInfoEntity::getMedicineCode).collect(Collectors.toSet());

        // for distinguish
        List<MedicineInfoEntity> insertList = medicineInfoList.stream()
                .filter(entity -> !repeatCodeList.contains(entity.getMedicineCode()))
                .toList();

        List<MedicineInfoEntity> needRegenerateList = medicineInfoList.stream()
                .filter(entity -> repeatCodeList.contains(entity.getMedicineCode()))
                .toList();

        return Pair.of(insertList, needRegenerateList);
    }

    @Override
    public Boolean update(MedicineSaveParam param) {
        if (param == null || CollUtil.isEmpty(param.getInfoList())) {
            return false;
        }

        // data prepare
        List<MedicineInfoSaveParam> paramList = param.getInfoList();
        ResidentInfoEntity residentInfo = residentInfoService.findByIdAndCheckNull(paramList.get(0).getResidentId());

        // 每组药品的代表 id
        List<Long> medicineIds = paramList.stream().map(MedicineInfoSaveParam::getId).toList();
        // 根据代表 id 查出药品信息
        List<MedicineInfoEntity> medicineInfoList = medicineInfoService.list(new LambdaQueryWrapper<MedicineInfoEntity>().in(MedicineInfoEntity::getId, medicineIds));
        Map<Long, MedicineInfoEntity> medicineInfoMap = medicineInfoList.stream().collect(Collectors.toMap(MedicineInfoEntity::getId, Function.identity()));

        // 查询出 resident 下所有未签名的药品(只能更新未签名药品)
        List<MedicineInfoEntity> unsignedList = listUnsignedMedicines(residentInfo.getId());
        // 获取代表 id 下的所有药品信息
        Map<Long, List<MedicineInfoEntity>> medicineLeaderMap = getMedicineLeaderMap(medicineInfoList, unsignedList);

        // 校验 paramList 中元素的 medicineQuantity 和 medicineMap 中对应列表的长度是否相同（medicineQuantity 不能增加或减少）
        checkMedicineQuantity(paramList, medicineLeaderMap);

        // 根据 paramList 校验 unsignedList 的药品是否做过修改，做过修改则需要将对应药品的所有签名作废
        Set<Long> modifiedMedicineIdSet = getModifiedMedicineIdSet(paramList, medicineInfoMap, medicineLeaderMap);
        if (modifiedMedicineIdSet.isEmpty()) {
            log.info("medicine update, nothing changed, param:{}", param);
            return true;
        }
        // 如有修改，则以前的签名作废
        medicineSignatureService.remove(new LambdaQueryWrapper<MedicineSignatureEntity>()
                .in(MedicineSignatureEntity::getMedicineId, modifiedMedicineIdSet)
        );
        medicationRecordService.remove(new LambdaQueryWrapper<MedicationRecordEntity>()
                .in(MedicationRecordEntity::getMedicineId, modifiedMedicineIdSet)
        );

        // 根据 paramList 更新药品信息
        updateMedicine(paramList, medicineLeaderMap);

        return true;
    }

    private Map<Long, List<MedicineInfoEntity>> getMedicineLeaderMap(List<MedicineInfoEntity> medicineInfoList, List<MedicineInfoEntity> unsignedList) {
        if (CollUtil.isEmpty(medicineInfoList) || CollUtil.isEmpty(unsignedList)) {
            return Collections.emptyMap();
        }

        Map<Pair<String, LocalDateTime>, List<MedicineInfoEntity>> unsignedGrouped = unsignedList.stream().collect(
                Collectors.groupingBy(medicine -> Pair.of(medicine.getMedicineName(), medicine.getRegisterTime()))
        );

        Map<Long, List<MedicineInfoEntity>> medicineLeaderMap = new HashMap<>();
        for (MedicineInfoEntity leaderMedicine : medicineInfoList) {
            List<MedicineInfoEntity> matchingUnsigned = unsignedGrouped.getOrDefault(Pair.of(leaderMedicine.getMedicineName(), leaderMedicine.getRegisterTime()), Collections.emptyList());
            medicineLeaderMap.put(leaderMedicine.getId(), matchingUnsigned);
        }
        return medicineLeaderMap;
    }

    private List<MedicineInfoEntity> listUnsignedMedicines(Long residentId) {
        // list all medicine
        List<MedicineInfoEntity> allMedicines = medicineInfoService.list(new LambdaQueryWrapper<MedicineInfoEntity>()
                .eq(MedicineInfoEntity::getResidentId, residentId)
        );
        if (CollUtil.isEmpty(allMedicines)) {
            return Collections.emptyList();
        }

        // list medicine signature info
        List<Long> allMedicineIds = allMedicines.stream().map(MedicineInfoEntity::getId).toList();
        List<MedicineSignatureEntity> existingSignatures = medicineSignatureService.list(new LambdaQueryWrapper<MedicineSignatureEntity>()
                .eq(MedicineSignatureEntity::getResidentId, residentId)
                .in(MedicineSignatureEntity::getMedicineId, allMedicineIds)
        );

        // group by medicineId
        Map<Long, List<MedicineSignatureEntity>> signaturesByMedicine = existingSignatures.stream().collect(
                Collectors.groupingBy(MedicineSignatureEntity::getMedicineId)
        );

        // filter unsigned
        return allMedicines.stream().filter(medicine -> {
            List<MedicineSignatureEntity> signatures = signaturesByMedicine.getOrDefault(medicine.getId(), Collections.emptyList());
            if (signatures.size() != 3) {
                return true;
            }
            Set<Integer> roleTypes = signatures.stream()
                    .map(MedicineSignatureEntity::getRoleType)
                    .collect(Collectors.toSet());
            return roleTypes.size() != 3;
        }).toList();
    }

    private void checkMedicineQuantity(List<MedicineInfoSaveParam> paramList, Map<Long, List<MedicineInfoEntity>> medicineLeaderMap) {
        for (MedicineInfoSaveParam saveParam : paramList) {
            List<MedicineInfoEntity> medicines = medicineLeaderMap.getOrDefault(saveParam.getId(), Collections.emptyList());
            if (medicines == null || medicines.size() != saveParam.getMedicineQuantity()) {
                log.warn("Update failed: Mismatched quantity for medicine: {} with registerTime: {}. Expected: {}, Actual: {}",
                        saveParam.getMedicineName(), saveParam.getRegisterTime(), saveParam.getMedicineQuantity(), medicines == null ? 0 : medicines.size());
                throw new BusinessException("药品数量不匹配，无法更新");
            }
        }
    }

    private Set<Long> getModifiedMedicineIdSet(List<MedicineInfoSaveParam> paramList,
                                               Map<Long, MedicineInfoEntity> oldMedicineInfoMap, Map<Long, List<MedicineInfoEntity>> medicineLeaderMap) {
        Set<Long> idSet = new HashSet<>();
        for (MedicineInfoSaveParam saveParam : paramList) {
            MedicineInfoEntity oldEntity = oldMedicineInfoMap.get(saveParam.getId());
            if (isMedicineModified(oldEntity, saveParam)) {
                List<MedicineInfoEntity> oldList = medicineLeaderMap.getOrDefault(saveParam.getId(), Collections.emptyList());
                idSet.addAll(oldList.stream().map(MedicineInfoEntity::getId).toList());
            }
        }
        return idSet;
    }

    private boolean isMedicineModified(MedicineInfoEntity oldEntity, MedicineInfoSaveParam saveParam) {
        return !Objects.equals(oldEntity.getMedicineName(), saveParam.getMedicineName()) ||
                !Objects.equals(oldEntity.getBatchNumber(), saveParam.getBatchNumber()) ||
                !Objects.equals(oldEntity.getSpecification(), saveParam.getSpecification()) ||
                !Objects.equals(oldEntity.getSpecificationUnit(), saveParam.getSpecificationUnit()) ||
                !Objects.equals(oldEntity.getExpirationTime(), saveParam.getExpirationTime()) ||
                !Objects.equals(oldEntity.getManufacturerInfo(), saveParam.getManufacturerInfo()) ||
                !Objects.equals(oldEntity.getHospitalInfo(), saveParam.getHospitalInfo()) ||
                !Objects.equals(oldEntity.getMedicineContentPer(), saveParam.getMedicineContentPer()) ||
                !Objects.equals(oldEntity.getMedicineContentPerUnit(), saveParam.getMedicineContentPerUnit()) ||
                !Objects.equals(oldEntity.getMedicineQuantityUnit(), saveParam.getMedicineQuantityUnit()) ||
                !Objects.equals(oldEntity.getDosage(), saveParam.getDosage()) ||
                !Objects.equals(oldEntity.getDosageUnit(), saveParam.getDosageUnit()) ||
                !Objects.equals(oldEntity.getUsageInfo(), saveParam.getUsageInfo()) ||
                !Objects.equals(oldEntity.getExtInfo().getSpecialUsageList(), saveParam.getExtInfo().getSpecialUsageList()) ||
                !Objects.equals(oldEntity.getExtInfo().getSpecificationList(), saveParam.getExtInfo().getSpecificationList());
    }

    private void updateMedicine(List<MedicineInfoSaveParam> infoList, Map<Long, List<MedicineInfoEntity>> medicineLeaderMap) {
        for (MedicineInfoSaveParam saveParam : infoList) {
            List<MedicineInfoEntity> medicines = medicineLeaderMap.getOrDefault(saveParam.getId(), Collections.emptyList());
            if (medicines != null) {
                medicines.forEach(medicine -> {
                    MedicineExtInfoDTO extInfo = updateMedicineExt(saveParam, medicine);
                    BeanUtils.copyProperties(saveParam, medicine, "id");
                    medicine.setExtInfo(extInfo);
                    medicine.setMedicineQuantity(null);
                    medicine.setRegisterTime(null);
                    medicine.setResidentId(null);
                    medicine.setResidentName(null);
                    medicine.setResidentNamePinyin(null);
                    medicine.setCreateTime(null);
                    medicine.setUpdateTime(null);
                });
            }
        }

        List<MedicineInfoEntity> updateList = medicineLeaderMap.values().stream().flatMap(Collection::stream).toList();
        medicineInfoService.updateBatchById(updateList);
    }

    private MedicineExtInfoDTO updateMedicineExt(MedicineInfoSaveParam saveParam, MedicineInfoEntity oldEntity) {
        MedicineExtInfoDTO updateExtInfo = saveParam.getExtInfo();
        MedicineExtInfoDTO oldExtInfo = oldEntity.getExtInfo();

        // 药品含量未改变
        if (Objects.equals(saveParam.getMedicineContentPer(), oldEntity.getMedicineContentPer())) {
            updateExtInfo.setInitNum(oldExtInfo.getInitNum());
            updateExtInfo.setStock(oldExtInfo.getStock());
            return updateExtInfo;
        }

        updateExtInfo.setInitNum(saveParam.getMedicineContentPer());
        updateExtInfo.setStock(updateStock(oldExtInfo.getInitNum(), saveParam.getMedicineContentPer(), oldExtInfo.getStock()));
        return updateExtInfo;
    }

    private String updateStock(Integer initNum, Integer lastNum, String stock) {
        if (lastNum == null || initNum == null) {
            return stock;
        }

        Fraction stockFraction = FractionUtil.convert2Fraction(stock);

        Fraction finalRes = stockFraction.add(Fraction.of(lastNum - initNum, 1));

        return FractionUtil.convertFromFraction(finalRes);
    }

    @Override
    public Boolean delete(MedicineInfoBatchParam param) {
        List<MedicineInfoEntity> list = medicineInfoService.list(new LambdaQueryWrapper<MedicineInfoEntity>().in(MedicineInfoEntity::getId, param.getIds()));
        if (CollUtil.isEmpty(list)) {
            return false;
        }

        boolean deleteRes = medicineInfoService.removeBatchByIds(param.getIds());
        // 删除对应签名
        medicineSignatureService.remove(new LambdaQueryWrapper<MedicineSignatureEntity>().in(MedicineSignatureEntity::getMedicineId, param.getIds()));
        // 更新对应服药单状态
        Set<String> medicineNames = list.stream().map(MedicineInfoEntity::getMedicineName).collect(Collectors.toSet());
        medicationRecordService.update(new LambdaUpdateWrapper<MedicationRecordEntity>()
                .set(MedicationRecordEntity::getValidStatus, BooleanEnum.FALSE.getType())
                .eq(MedicationRecordEntity::getResidentId, list.get(0).getResidentId())
                .in(MedicationRecordEntity::getMedicineName, medicineNames)
                .eq(MedicationRecordEntity::getValidStatus, BooleanEnum.TRUE.getType())
        );

        return deleteRes;
    }

    @Override
    public List<MedicineInfoVO> allList(MedicineInfoListParam param) {
        // build search condition
        LambdaQueryWrapper<MedicineInfoEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MedicineInfoEntity::getInstitutionId, param.getInstitutionId());
        fillExpirationTypeCondition(param.getExpirationType(), wrapper);
        fillWriteOffStatusCondition(wrapper);
        fillSortTypeCondition(param.getSortConditionList(), wrapper);

        // page search
        List<MedicineInfoEntity> medicineInfoList = medicineInfoService.page(new Page<>(param.getPageNo(), param.getPageSize()), wrapper).getRecords();
        if (CollUtil.isEmpty(medicineInfoList)) {
            return Collections.emptyList();
        }

        // build vo list
        List<MedicineInfoVO> voList = medicineInfoList.stream().map(MedicineInfoVO::convert2VO).toList();

        //  fill medicine signature
        fillMedicineSignature(voList, param.getInstitutionId(), null);

        return voList;
    }

    private void fillExpirationTypeCondition(Integer expirationType, LambdaQueryWrapper<MedicineInfoEntity> wrapper) {
        LocalDateTime startOfDay = LocalDateTime.now().toLocalDate().atStartOfDay();
        LocalDateTime endOfDay = LocalDateTimeUtil.endOfDay(LocalDateTime.now(), true);
        if (MedicineExpirationTypeEnum.EXPIRED.getType().equals(expirationType)) {
            wrapper.eq(MedicineInfoEntity::getExpirationTime, startOfDay);
        }
        if (MedicineExpirationTypeEnum.EXPIRING_SOON.getType().equals(expirationType)) {
            wrapper.between(MedicineInfoEntity::getExpirationTime, startOfDay.plusMinutes(1), endOfDay.plusDays(179));
        }
        if (MedicineExpirationTypeEnum.NO_EXPIRATION.getType().equals(expirationType)) {
            wrapper.ge(MedicineInfoEntity::getExpirationTime, startOfDay.plusDays(180));
        }
    }

    private void fillWriteOffStatusCondition(LambdaQueryWrapper<MedicineInfoEntity> wrapper) {
        wrapper.eq(MedicineInfoEntity::getWriteOffStatus, 0);
    }

    private void fillSortTypeCondition(List<MedicineInfoSortConditionParam> sortConditionList, LambdaQueryWrapper<MedicineInfoEntity> wrapper) {
        if (CollUtil.isEmpty(sortConditionList)) {
            return;
        }
        sortConditionList.forEach(sortCondition -> {
            if (MedicineSortTypeEnum.LOCATION_INFO.getType().equals(sortCondition.getSortType())) {
                wrapper.orderBy(true, Objects.equals(sortCondition.getIsAsc(), 1), MedicineInfoEntity::getLocationInfo);
            } else if (MedicineSortTypeEnum.RESIDENT_NAME_PINYIN.getType().equals(sortCondition.getSortType())) {
                wrapper.orderBy(true, Objects.equals(sortCondition.getIsAsc(), 1), MedicineInfoEntity::getResidentNamePinyin);
            } else if (MedicineSortTypeEnum.EXPIRATION_DATE.getType().equals(sortCondition.getSortType())) {
                wrapper.orderBy(true, Objects.equals(sortCondition.getIsAsc(), 1), MedicineInfoEntity::getExpirationTime);
            } else {
                wrapper.orderByAsc(MedicineInfoEntity::getId);
            }
        });
    }

    private void fillMedicineSignature(List<MedicineInfoVO> voList, Long institutionId, Long residentId) {
        if (CollUtil.isEmpty(voList)) {
            return;
        }

        // get signature list
        List<Long> medicineIds = voList.stream().map(MedicineInfoVO::getId).toList();
        List<MedicineSignatureEntity> medicineSignatureList = medicineSignatureService.list(
                new LambdaQueryWrapper<MedicineSignatureEntity>()
                        .eq(MedicineSignatureEntity::getInstitutionId, institutionId)
                        .eq(residentId != null, MedicineSignatureEntity::getResidentId, residentId)
                        .in(MedicineSignatureEntity::getMedicineId, medicineIds)
        );
        if (CollUtil.isEmpty(medicineSignatureList)) {
            return;
        }

        // group by medicine id
        Map<Long, List<MedicineSignatureEntity>> medicineSignatureMap = medicineSignatureList.stream()
                .collect(Collectors.groupingBy(MedicineSignatureEntity::getMedicineId));

        // set signature for vo
        voList.forEach(vo ->
                vo.setMedicineSignatureList(Optional.ofNullable(medicineSignatureMap.get(vo.getId()))
                        .orElse(Collections.emptyList())
                        .stream()
                        .map(MedicineSignatureVO::convert2VO)
                        .toList()
                ));
    }

    private Integer calNeedSignatureEntry(Integer medicineType, List<MedicineInfoVO> voList) {
        if (CollUtil.isEmpty(voList)) {
            return 0;
        }

        boolean matchRes = voList.stream().anyMatch(vo -> {
            List<MedicineSignatureVO> signatureList = vo.getMedicineSignatureList();
            if (CollUtil.isEmpty(signatureList)) {
                return true;
            }

            Set<Integer> roleSet = signatureList.stream().map(MedicineSignatureVO::getRoleType).collect(Collectors.toSet());
            if (MedicineTypeEnum.DISPENSING_OF_MEDICINES.getType().equals(medicineType)) {
                return !roleSet.containsAll(Arrays.asList(
                        MedicineSignatureRoleTypeEnum.MANAGER.getType(),
                        MedicineSignatureRoleTypeEnum.PHARMACY.getType()
                ));
            }
            return !roleSet.containsAll(Arrays.asList(
                    MedicineSignatureRoleTypeEnum.FAMILY_MEMBER.getType(),
                    MedicineSignatureRoleTypeEnum.MANAGER.getType(),
                    MedicineSignatureRoleTypeEnum.PHARMACY.getType()
            ));
        });
        return matchRes ? 1 : 0;
    }

    @Override
    public List<MedicineInfoVO> listByIds(MedicineListParam param) {
        List<MedicineInfoEntity> entityList = medicineInfoService.listByIds(param.getMedicineIds());
        if (param.getNeedGroup() == null || param.getNeedGroup() == 0) {
            return entityList.stream().map(MedicineInfoVO::convert2VO).toList();
        }
        return groupedMedicineListByMedicineNameAndRegisterTime(entityList.stream().map(MedicineInfoVO::convert2VO).toList());
    }

    @Override
    public MedicineListVO list4Resident(MedicineInfoList4ResidentParam param) {
        if (param == null) {
            return null;
        }

        // check resident info
        ResidentInfoEntity resident = residentInfoService.findByIdAndCheckNull(param.getResidentId());

        // get medicine list
        List<MedicineInfoEntity> list = medicineInfoService.list(
                new LambdaQueryWrapper<MedicineInfoEntity>()
                        .eq(MedicineInfoEntity::getResidentId, param.getResidentId())
                        .eq(param.getMedicineType() != 0, MedicineInfoEntity::getMedicineType, param.getMedicineType())
                        .eq(param.getMedicineProperty() != 0, MedicineInfoEntity::getMedicineProperty, param.getMedicineProperty())
                        .orderByAsc(MedicineInfoEntity::getId)
        );

        // convert to vo list
        List<MedicineInfoVO> voList = Optional.ofNullable(list).orElse(Collections.emptyList()).stream().map(MedicineInfoVO::convert2VO).toList();

        // fill medicine signature
        fillMedicineSignature(voList, resident.getInstitutionId(), resident.getId());
        // fill needSignatureEntry field
        Integer needSignatureEntry = calNeedSignatureEntry(param.getMedicineType(), voList);

        // 不需要分组
        if (CollUtil.isEmpty(voList) || MedicineGroupStrategyEnum.NO_GROUP.getType().equals(param.getGroupStrategy())) {
            return MedicineListVO.builder().medicineInfoList(voList).needSignatureEntry(needSignatureEntry).build();
        }

        // filter expired、write-off medicine
        LocalDateTime endOfDay = LocalDateTimeUtil.endOfDay(LocalDateTime.now(), true);
        List<MedicineInfoVO> filterList = voList.stream()
                .filter(medicineInfoVO -> medicineInfoVO.getWriteOffStatus() != 1 && medicineInfoVO.getExpirationTime().isAfter(endOfDay))
                .toList();

        // group by medicine name
        List<MedicineInfoVO> groupedMedicineList;
        if (MedicineGroupStrategyEnum.BY_MEDICINE_NAME_AND_REDUCE_STOCK.getType().equals(param.getGroupStrategy())) {
            groupedMedicineList = groupedMedicineListByMedicineName(resident.getId(), filterList);
        } else if (MedicineGroupStrategyEnum.BY_MEDICINE_NAME_REGISTER_TIME.getType().equals(param.getGroupStrategy())) {
            groupedMedicineList = groupedMedicineListByMedicineNameAndRegisterTime(filterList);
        } else {
            groupedMedicineList = Collections.emptyList();
        }

        return MedicineListVO.builder().medicineInfoList(groupedMedicineList).build();
    }

    private List<MedicineInfoVO> groupedMedicineListByMedicineNameAndRegisterTime(List<MedicineInfoVO> voList) {
        Map<Pair<String, LocalDateTime>, MedicineInfoVO> groupedMap = new LinkedHashMap<>();
        for (MedicineInfoVO medicine : voList) {
            Pair<String, LocalDateTime> key = Pair.of(medicine.getMedicineName(), medicine.getRegisterTime());
            if (groupedMap.containsKey(key)) {
                MedicineInfoVO existing = groupedMap.get(key);
                // 数量递增
                existing.setMedicineQuantity(existing.getMedicineQuantity() + 1);
            } else {
                groupedMap.put(key, medicine);
            }
        }
        return groupedMap.values().stream().toList();
    }

    private List<MedicineInfoVO> groupedMedicineListByMedicineName(Long residentId, List<MedicineInfoVO> voList) {
        // 库存计算时，需要展示同名药品的最新信息（用法、用量）
        List<MedicineInfoVO> sortedList = voList.stream()
                .sorted((v1, v2) -> v2.getId().compareTo(v1.getId()))
                .toList();

        // 查询库存、服药记录
        List<String> medicineNames = sortedList.stream().map(MedicineInfoVO::getMedicineName).toList();
//        Map<String, String> medicineStock = findMedicineStock(residentId, medicineNames);
        Map<String, String> medicineUseNum = findMedicineUseNum(residentId, medicineNames);
        Map<String, Integer> medicineRecord = findMedicineRecord(residentId, medicineNames);

        Map<String, MedicineInfoVO> groupedMap = new LinkedHashMap<>();

        for (MedicineInfoVO medicine : sortedList) {
            if (groupedMap.containsKey(medicine.getMedicineName())) {
                MedicineInfoVO existing = groupedMap.get(medicine.getMedicineName());
                existing.setMedicineQuantity(existing.getMedicineQuantity() + 1);
                dealMedicineStock(existing.getExtInfo(), medicine.getExtInfo());
            } else {
                groupedMap.put(medicine.getMedicineName(), medicine);
            }
        }

        // 填充服药单库存信息
        for (MedicineInfoVO medicine : groupedMap.values()) {
            if (medicineUseNum.containsKey(medicine.getMedicineName())
                    && BooleanEnum.TRUE.getType().equals(medicineRecord.getOrDefault(medicine.getMedicineName(), BooleanEnum.FALSE.getType()))) {
                MedicineExtInfoDTO extInfo = medicine.getExtInfo();
                // 计算库存 totalNum - useNum
                extInfo.setStock(calRealStock(extInfo.getStock(), medicineUseNum.get(medicine.getMedicineName())));
                medicine.setExtInfo(extInfo);
                groupedMap.put(medicine.getMedicineName(), medicine);
            }
        }
        return groupedMap.values().stream().toList();
    }

    private String calRealStock(String totalNum, String useNum) {
        Fraction totalNumFraction = FractionUtil.convert2Fraction(totalNum);
        Fraction useNumFraction = FractionUtil.convert2Fraction(useNum);
        String stock = FractionUtil.convertFromFraction(totalNumFraction.subtract(useNumFraction));
        if (stock.startsWith("-") || "0".equals(stock)) {
            return "0-0-0";
        }
        return stock;
    }

    private Map<String, String> findMedicineStock(Long residentId, List<String> medicineNames) {
        if (CollUtil.isEmpty(medicineNames)) {
            return Collections.emptyMap();
        }
        List<MedicineStockEntity> stockList = medicineStockService.list(new LambdaQueryWrapper<MedicineStockEntity>()
                .eq(MedicineStockEntity::getResidentId, residentId)
                .in(MedicineStockEntity::getMedicineName, medicineNames)
        );

        return stockList.stream().collect(Collectors.toMap(
                MedicineStockEntity::getMedicineName,
                MedicineStockEntity::getMedicineStock,
                (v1, v2) -> v2
        ));
    }

    private Map<String, String> findMedicineUseNum(Long residentId, List<String> medicineNames) {
        if (CollUtil.isEmpty(medicineNames)) {
            return Collections.emptyMap();
        }
        List<MedicineStockEntity> stockList = medicineStockService.list(new LambdaQueryWrapper<MedicineStockEntity>()
                .eq(MedicineStockEntity::getResidentId, residentId)
                .in(MedicineStockEntity::getMedicineName, medicineNames)
        );

        return stockList.stream().collect(Collectors.toMap(
                MedicineStockEntity::getMedicineName,
                MedicineStockEntity::getTotalUseNum,
                (v1, v2) -> v2
        ));
    }

    private Map<String, Integer> findMedicineRecord(Long residentId, List<String> medicineNames) {
        if (CollUtil.isEmpty(medicineNames)) {
            return Collections.emptyMap();
        }
        List<MedicationRecordEntity> recordList = medicationRecordService.list(new LambdaQueryWrapper<MedicationRecordEntity>()
                .eq(MedicationRecordEntity::getResidentId, residentId)
                .in(MedicationRecordEntity::getMedicineName, medicineNames)
        );

        return recordList.stream().collect(Collectors.toMap(
                MedicationRecordEntity::getMedicineName,
                MedicationRecordEntity::getValidStatus,
                (v1, v2) -> v2
        ));
    }

    private void dealMedicineStock(MedicineExtInfoDTO existingExtInfo, MedicineExtInfoDTO newExtInfo) {
        if (existingExtInfo == null || newExtInfo == null) {
            return;
        }
        Fraction addRes = FractionUtil.convert2Fraction(existingExtInfo.getStock()).add(FractionUtil.convert2Fraction(newExtInfo.getStock()));
        String stock = FractionUtil.convertFromFraction(addRes);
        existingExtInfo.setStock("0-0-1".equals(stock) ? "0-0-0" : stock);
    }

    @Override
    public MedicineListVO listUnsigned4Resident(MedicineInfoList4ResidentParam param) {
        if (param == null) {
            return null;
        }

        // get all medicines for the resident
        List<MedicineInfoEntity> allList = medicineInfoService.list(
                new LambdaQueryWrapper<MedicineInfoEntity>()
                        .eq(MedicineInfoEntity::getResidentId, param.getResidentId())
                        .eq(MedicineInfoEntity::getMedicineType, param.getMedicineType())
                        .eq(MedicineInfoEntity::getMedicineProperty, param.getMedicineProperty())
        );
        if (CollUtil.isEmpty(allList)) {
            return MedicineListVO.builder().medicineInfoList(Collections.emptyList()).build();
        }

        // get all signed for the resident's medicines
        // Group signatures by medicineId
        List<MedicineSignatureEntity> signedList = medicineSignatureService.list(
                new LambdaQueryWrapper<MedicineSignatureEntity>()
                        .eq(MedicineSignatureEntity::getResidentId, param.getResidentId())
                        .in(MedicineSignatureEntity::getMedicineId, allList.stream().map(MedicineInfoEntity::getId).toList())
        );
        Map<Long, List<MedicineSignatureEntity>> signedListByMedicine = signedList.stream()
                .collect(Collectors.groupingBy(MedicineSignatureEntity::getMedicineId));

        // filter out signed medicines
        List<MedicineInfoEntity> unsignedMedicines = allList.stream()
                .filter(medicine -> {
                    List<MedicineSignatureEntity> signatures = signedListByMedicine.get(medicine.getId());
                    // not enough signatures
                    if (CollUtil.isEmpty(signatures) || signatures.size() != 3) {
                        return true;
                    }
                    // check for distinct role types
                    Set<Integer> roleTypes = signatures.stream()
                            .map(MedicineSignatureEntity::getRoleType)
                            .collect(Collectors.toSet());
                    return roleTypes.size() != 3;
                })
                .toList();

        // convert to vo list
        List<MedicineInfoVO> voList = unsignedMedicines.stream()
                .map(MedicineInfoVO::convert2VO)
                .toList();

        if (MedicineGroupStrategyEnum.NO_GROUP.getType().equals(param.getNeedGroup())) {
            return MedicineListVO.builder()
                    .medicineInfoList(voList)
                    .build();
        }
        return MedicineListVO.builder()
                .medicineInfoList(groupedMedicineListByMedicineNameAndRegisterTime(voList))
                .build();
    }


    @Override
    public List<MedicineInfoVO> runOutSoonList(MedicineInfoListParam param) {
        LambdaQueryWrapper<MedicineRunOutSoonLogEntity> wrapper = new LambdaQueryWrapper<MedicineRunOutSoonLogEntity>()
                .eq(MedicineRunOutSoonLogEntity::getInstitutionId, param.getInstitutionId())
                .gt(MedicineRunOutSoonLogEntity::getCreateTime, LocalDateTimeUtil.beginOfDay(LocalDateTime.now()));

        param.getSortConditionList().forEach(sortCondition -> {
            if (MedicineSortTypeEnum.LOCATION_INFO.getType().equals(sortCondition.getSortType())) {
                wrapper.orderBy(true, Objects.equals(sortCondition.getIsAsc(), 1), MedicineRunOutSoonLogEntity::getLocationInfo);
            } else if (MedicineSortTypeEnum.RESIDENT_NAME_PINYIN.getType().equals(sortCondition.getSortType())) {
                wrapper.orderBy(true, Objects.equals(sortCondition.getIsAsc(), 1), MedicineRunOutSoonLogEntity::getResidentNamePinyin);
            } else {
                wrapper.orderByAsc(MedicineRunOutSoonLogEntity::getId);
            }
        });

        List<MedicineRunOutSoonLogEntity> records = medicineRunOutSoonLogService.page(new Page<>(param.getPageNo(), param.getPageSize()), wrapper).getRecords();
        return records.stream().map(MedicineInfoVO::convert2VO).toList();
    }

    @Override
    public Map<Long, List<MedicineInfoEntity>> listSignedMedicineInfoByResidentId(List<Long> residentIdList) {
        if (CollUtil.isEmpty(residentIdList)) {
            return Collections.emptyMap();
        }
        LocalDateTime startOfDay = LocalDateTime.now().toLocalDate().atStartOfDay();
        List<MedicineInfoEntity> allList = medicineInfoService.list(new LambdaQueryWrapper<MedicineInfoEntity>()
                .eq(MedicineInfoEntity::getWriteOffStatus, BooleanEnum.FALSE.getType())
                .in(MedicineInfoEntity::getResidentId, residentIdList)
                .ge(MedicineInfoEntity::getExpirationTime, startOfDay)
        );
        if (CollUtil.isEmpty(allList)) {
            return Collections.emptyMap();
        }
        Map<Long, List<MedicineInfoEntity>> groupMedicineMap = allList.stream().collect(Collectors.groupingBy(MedicineInfoEntity::getResidentId));

        List<MedicineSignatureEntity> signatureList = medicineSignatureService.list(new LambdaQueryWrapper<MedicineSignatureEntity>()
                .in(MedicineSignatureEntity::getMedicineId, allList.stream().map(MedicineInfoEntity::getId).toList())
        );
        Map<Long, List<MedicineSignatureEntity>> signatureListByMedicine = signatureList.stream().collect(Collectors.groupingBy(MedicineSignatureEntity::getMedicineId));

        // filter unsigned medicines
        return groupMedicineMap.keySet().stream().collect(Collectors.toMap(
                Function.identity(),
                residentId -> {
                    List<MedicineInfoEntity> medicineInfoEntityList = groupMedicineMap.get(residentId);
                    return medicineInfoEntityList.stream()
                            .filter(medicine -> {
                                List<MedicineSignatureEntity> signatures = signatureListByMedicine.get(medicine.getId());
                                if (CollUtil.isEmpty(signatures)) {
                                    return false;
                                }
                                Set<Integer> roleTypes = signatures.stream()
                                        .map(MedicineSignatureEntity::getRoleType)
                                        .collect(Collectors.toSet());
                                if (MedicineTypeEnum.DISPENSING_OF_MEDICINES.getType().equals(medicine.getMedicineType())) {
                                    return roleTypes.containsAll(Arrays.asList(
                                            MedicineSignatureRoleTypeEnum.MANAGER.getType(),
                                            MedicineSignatureRoleTypeEnum.PHARMACY.getType()
                                    ));
                                }
                                return roleTypes.containsAll(Arrays.asList(
                                        MedicineSignatureRoleTypeEnum.FAMILY_MEMBER.getType(),
                                        MedicineSignatureRoleTypeEnum.MANAGER.getType(),
                                        MedicineSignatureRoleTypeEnum.PHARMACY.getType()
                                ));
                            })
                            .toList();
                }
        ));
    }


}
