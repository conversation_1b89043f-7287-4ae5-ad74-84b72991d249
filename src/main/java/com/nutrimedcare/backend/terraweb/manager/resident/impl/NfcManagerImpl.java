package com.nutrimedcare.backend.terraweb.manager.resident.impl;

import com.alibaba.excel.util.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nutrimedcare.backend.terraweb.common.BizErrorCode;
import com.nutrimedcare.backend.terraweb.dao.CareInstitutionEntity;
import com.nutrimedcare.backend.terraweb.dao.ResidentInfoEntity;
import com.nutrimedcare.backend.terraweb.dao.caregiver.CaregiverInfoEntity;
import com.nutrimedcare.backend.terraweb.dao.caregiver.CaregiverRelatedEntity;
import com.nutrimedcare.backend.terraweb.dao.medicine.MedicationRecordEntity;
import com.nutrimedcare.backend.terraweb.dao.medicine.MedicineInfoEntity;
import com.nutrimedcare.backend.terraweb.dao.resident.ResidentCheckInInfoEntity;
import com.nutrimedcare.backend.terraweb.dao.resident.ResidentNfcRelatedEntity;
import com.nutrimedcare.backend.terraweb.dto.institution.CareInstitutionExtInfoDTO;
import com.nutrimedcare.backend.terraweb.dto.medicine.MedicineRecordDetailDTO;
import com.nutrimedcare.backend.terraweb.dto.medicine.MedicineUsageDTO;
import com.nutrimedcare.backend.terraweb.enums.BooleanEnum;
import com.nutrimedcare.backend.terraweb.enums.medicine.record.MedicationRecordTimeTypeEnum;
import com.nutrimedcare.backend.terraweb.exception.BusinessException;
import com.nutrimedcare.backend.terraweb.manager.resident.NfcManager;
import com.nutrimedcare.backend.terraweb.param.nfc.NfcBindParam;
import com.nutrimedcare.backend.terraweb.param.nfc.NfcCheckInConfirmParam;
import com.nutrimedcare.backend.terraweb.param.nfc.NfcCheckInParam;
import com.nutrimedcare.backend.terraweb.param.nfc.NfcTagDataParam;
import com.nutrimedcare.backend.terraweb.service.CareInstitutionService;
import com.nutrimedcare.backend.terraweb.service.MedicationRecordService;
import com.nutrimedcare.backend.terraweb.service.MedicineInfoService;
import com.nutrimedcare.backend.terraweb.service.ResidentInfoService;
import com.nutrimedcare.backend.terraweb.service.caregiver.CaregiverInfoService;
import com.nutrimedcare.backend.terraweb.service.caregiver.CaregiverRelatedService;
import com.nutrimedcare.backend.terraweb.service.resident.ResidentCheckInInfoService;
import com.nutrimedcare.backend.terraweb.service.resident.ResidentNfcRelatedService;
import com.nutrimedcare.backend.terraweb.util.EncryptUtil;
import com.nutrimedcare.backend.terraweb.util.MedicineRecordTimeUtil;
import com.nutrimedcare.backend.terraweb.vo.medicine.MedicineInfoVO;
import com.nutrimedcare.backend.terraweb.vo.nfc.NfcCheckInConfirmDataVO;
import lombok.extern.slf4j.Slf4j;
import org.dromara.hutool.core.collection.CollUtil;
import org.dromara.hutool.core.lang.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class NfcManagerImpl implements NfcManager {

    @Autowired
    private ResidentInfoService residentInfoService;

    @Autowired
    private ResidentNfcRelatedService residentNfcRelatedService;

    @Autowired
    private EncryptUtil encryptUtil;

    @Autowired
    private CaregiverRelatedService caregiverRelatedService;

    @Autowired
    private ResidentCheckInInfoService residentCheckInInfoService;

    @Autowired
    private CaregiverInfoService caregiverInfoService;

    @Autowired
    private MedicationRecordService medicationRecordService;

    @Autowired
    private CareInstitutionService careInstitutionService;

    @Autowired
    private MedicineInfoService medicineInfoService;

    @Override
    public Boolean bindResident(NfcBindParam param) {
        if (param == null) {
            return false;
        }

        residentNfcRelatedService.remove(new LambdaQueryWrapper<ResidentNfcRelatedEntity>()
                .eq(ResidentNfcRelatedEntity::getNfcUniqueId, param.getNfcUniqueId())
                .or().eq(ResidentNfcRelatedEntity::getResidentId, param.getResidentId())
        );

        ResidentInfoEntity residentInfo = residentInfoService.findByIdAndCheckNull(param.getResidentId());

        List<ResidentNfcRelatedEntity> residentRelatedList = residentNfcRelatedService.list(new LambdaQueryWrapper<ResidentNfcRelatedEntity>()
                .eq(ResidentNfcRelatedEntity::getNfcUniqueId, param.getNfcUniqueId())
        );
        if (CollUtil.isNotEmpty(residentRelatedList)) {
            throw new BusinessException("该 NFC 卡片已绑定");
        }

        boolean saveRes = residentNfcRelatedService.save(buildResidentNfcRelatedEntity(param, residentInfo.getInstitutionId()));
        if (!saveRes) {
            log.warn("nfc manager, fail to bind resident, param:{}", param);
        }
        return true;
    }

    private ResidentNfcRelatedEntity buildResidentNfcRelatedEntity(NfcBindParam param, Long institutionId) {
        ResidentNfcRelatedEntity entity = new ResidentNfcRelatedEntity();
        entity.setNfcUniqueId(param.getNfcUniqueId());
        entity.setInstitutionId(institutionId);
        entity.setResidentId(param.getResidentId());
        return entity;
    }

    @Override
    public Boolean rebindResident(NfcBindParam param) {
        if (param == null) {
            return false;
        }

        // 清除绑定信息
        residentNfcRelatedService.remove(new LambdaQueryWrapper<ResidentNfcRelatedEntity>()
                .eq(ResidentNfcRelatedEntity::getNfcUniqueId, param.getNfcUniqueId())
        );

        return bindResident(param);
    }

    @Override
    public Boolean checkBindRelation(NfcBindParam param) {
        if (param == null) {
            return true;
        }
        List<ResidentNfcRelatedEntity> residentList = residentNfcRelatedService.list(new LambdaQueryWrapper<ResidentNfcRelatedEntity>()
                .eq(ResidentNfcRelatedEntity::getNfcUniqueId, param.getNfcUniqueId())
        );
        // 未存在绑定关系
        if (CollUtil.isEmpty(residentList)) {
            return false;
        }
        // 已存在绑定关系，但为当前入住人
        ResidentNfcRelatedEntity entity = residentList.get(0);
        return !Objects.equals(entity.getResidentId(), param.getResidentId());
    }

    @Override
    public Boolean checkNfcTagData(NfcTagDataParam param) {
        if (param == null) {
            return false;
        }

        try {
            String residentId = encryptUtil.aesDecrypt(param.getResidentIdStr());
            residentInfoService.findByIdAndCheckNull(Long.parseLong(residentId));
        } catch (Exception e) {
            return false;
        }

        // 校验通过，存在该入住人
        return true;
    }

    @Override
    public NfcCheckInConfirmDataVO residentCheckInDataDetail(NfcCheckInConfirmParam param) {
        // 查询入住人信息
        ResidentInfoEntity residentInfo = residentInfoService.findByIdAndCheckNull(param.getResidentId());
        // 查询机构信息
        CareInstitutionEntity caseInstitution = careInstitutionService.findByIdAndCheckNull(residentInfo.getInstitutionId());

        // 根据当前时间计算服药时间段
        CareInstitutionExtInfoDTO extInfo = caseInstitution.getExtInfo();
        Integer medicationTime = MedicineRecordTimeUtil.calMedicationTime(LocalDateTime.now(), extInfo == null ? Collections.emptyMap() : extInfo.getMedicationTakeTimeMap());
        if (medicationTime == null) {
            throw new BusinessException(BizErrorCode.NFC_CHECK_IN_NO_TAKE_TIME);
        }

        // 查询入住人对应所有有效的用药记录
        List<MedicationRecordEntity> recordList = medicationRecordService.list(new LambdaQueryWrapper<MedicationRecordEntity>()
                .eq(MedicationRecordEntity::getResidentId, param.getResidentId())
                .eq(MedicationRecordEntity::getValidStatus, BooleanEnum.TRUE.getType())
        );
        if (CollUtil.isEmpty(recordList)) {
            throw new BusinessException(BizErrorCode.NFC_CHECK_IN_NO_BIND_MEDICINE_RECORD);
        }

        // 根据服药时间段匹配用药记录
        List<Pair<MedicationRecordEntity, Integer>> filterList = filterMedicationRecordByTakeTimeType(medicationTime, recordList);
        if (CollUtil.isEmpty(filterList)) {
            return buildNfcCheckInConfirmDataVO(medicationTime, Collections.emptyList());
        }

        // 根据用药记录获取用量、用量单位
        return buildNfcCheckInConfirmDataVO(medicationTime, buildCheckInDataMedicineInfoList(filterList));
    }

    private List<MedicineInfoVO> buildCheckInDataMedicineInfoList(List<Pair<MedicationRecordEntity, Integer>> filterList) {
        return filterList.stream().map(pair -> {
            MedicationRecordEntity medicationRecord = pair.getLeft();
            MedicineRecordDetailDTO usageDetail = medicationRecord.getUsageDetail().get(pair.getRight());

            // 存在 usageInfo 信息
            MedicineUsageDTO recordUsageInfo = usageDetail.getUsageInfo();
            if (recordUsageInfo != null
                    && StringUtils.isNotBlank(recordUsageInfo.getDosageInfo())
                    && StringUtils.isNotBlank(recordUsageInfo.getDosageUnit())) {
                return buildMedicineInfoVO(medicationRecord.getMedicineName(), recordUsageInfo.getDosageInfo(), recordUsageInfo.getDosageUnit());
            }

            // 存在 usageInfoSnap
            MedicineUsageDTO usageInfoSnap = usageDetail.getUsageInfoSnap();
            if (Objects.nonNull(usageInfoSnap)) {
                return buildMedicineInfoVO(medicationRecord.getMedicineName(), usageInfoSnap.getDosageInfo(), usageInfoSnap.getDosageUnit());
            }

            // 老数据，从数据库中查询
            List<MedicineInfoEntity> medicineList = medicineInfoService.list(new LambdaQueryWrapper<MedicineInfoEntity>()
                    .eq(MedicineInfoEntity::getResidentId, medicationRecord.getResidentId())
                    .eq(MedicineInfoEntity::getMedicineName, medicationRecord.getMedicineName())
                    .orderByDesc(MedicineInfoEntity::getId)
            );
            MedicineInfoEntity medicineInfo = medicineList.get(0);
            Pair<String, String> usageInfoPair = findMedicineUsageInfo(medicineInfo, pair.getRight());

            return buildMedicineInfoVO(medicationRecord.getMedicineName(), usageInfoPair.getLeft(), usageInfoPair.getRight());
        }).toList();
    }

    private Pair<String, String> findMedicineUsageInfo(MedicineInfoEntity medicineInfo, Integer usageDetailIndex) {
        if (medicineInfo == null || usageDetailIndex == null) {
            return Pair.of("", "");
        }
        if (usageDetailIndex == 0) {
            return Pair.of(medicineInfo.getDosage(), medicineInfo.getDosageUnit());
        }

        try {
            List<MedicineUsageDTO> specialUsageList = medicineInfo.getExtInfo().getSpecialUsageList();
            if (CollUtil.isEmpty(specialUsageList)) {
                return Pair.of("", "");
            }

            MedicineUsageDTO medicineUsageDTO = specialUsageList.get(usageDetailIndex - 1);
            return Pair.of(medicineUsageDTO.getDosageInfo(), medicineUsageDTO.getDosageUnit());
        } catch (Exception e) {
            log.warn("nfc manager, fail to match medicine usage info, medicineInfo:{}, usageDetailIndex:{}", medicineInfo, usageDetailIndex, e);
            return Pair.of("", "");
        }
    }

    private MedicineInfoVO buildMedicineInfoVO(String medicineName, String dosage, String dosageUnit) {
        MedicineInfoVO medicineInfoVO = new MedicineInfoVO();
        medicineInfoVO.setMedicineName(medicineName);
        medicineInfoVO.setDosage(dosage);
        medicineInfoVO.setDosageUnit(dosageUnit);
        return medicineInfoVO;
    }

    /**
     * @return Left: 用药记录, Right: 匹配到的 UsageDetail 用法索引
     */
    private List<Pair<MedicationRecordEntity, Integer>> filterMedicationRecordByTakeTimeType(Integer medicationTime, List<MedicationRecordEntity> recordList) {
        List<Pair<MedicationRecordEntity, Integer>> filterList = new ArrayList<>();

        recordList.forEach(medicationRecord -> {
            List<MedicineRecordDetailDTO> usageDetail = medicationRecord.getUsageDetail();
            for (int i = 0; i < usageDetail.size(); i++) {
                MedicineRecordDetailDTO detailDTO = usageDetail.get(i);
                List<Integer> medicationTimeList = detailDTO.getMedicationTimeList();
                if (medicationTimeList.contains(MedicationRecordTimeTypeEnum.NO_EAT.getType())) {
                    continue;
                }
                if (medicationTimeList.contains(medicationTime)) {
                    filterList.add(Pair.of(medicationRecord, i));
                }
            }
        });

        return filterList;
    }

    private NfcCheckInConfirmDataVO buildNfcCheckInConfirmDataVO(Integer medicationTime, List<MedicineInfoVO> medicineList) {
        NfcCheckInConfirmDataVO vo = new NfcCheckInConfirmDataVO();
        vo.setMedicineTimeType(medicationTime);
        vo.setMedicineList(medicineList);
        return vo;
    }

    @Override
    public Boolean checkIn(NfcCheckInParam param) {
        if (param == null) {
            return false;
        }

        ResidentInfoEntity residentInfo = residentInfoService.findByIdAndCheckNull(param.getResidentId());

        CaregiverInfoEntity caregiverInfo = caregiverInfoService.getById(param.getCaregiverId());
        if (caregiverInfo == null) {
            throw new BusinessException("该护理员不存在");
        }

        // 校验是否存在绑定关系
        List<CaregiverRelatedEntity> caregiverList = caregiverRelatedService.list(new LambdaQueryWrapper<CaregiverRelatedEntity>()
                .eq(CaregiverRelatedEntity::getResidentId, residentInfo.getId())
                .eq(CaregiverRelatedEntity::getCaregiverId, param.getCaregiverId())
        );
        if (CollUtil.isEmpty(caregiverList)) {
            throw new BusinessException(BizErrorCode.NFC_CHECK_IN_NOT_BIND_CAREGIVER);
        }

        // 校验该时段是否已经记录服药状态
        LocalDateTime startOfDay = LocalDateTime.now().toLocalDate().atStartOfDay();
        List<ResidentCheckInInfoEntity> checkInDataList = residentCheckInInfoService.list(new LambdaQueryWrapper<ResidentCheckInInfoEntity>()
                .eq(ResidentCheckInInfoEntity::getResidentId, param.getResidentId())
                .eq(ResidentCheckInInfoEntity::getTakeTimeType, param.getTakeTimeType())
                .eq(ResidentCheckInInfoEntity::getMedicineTakeStatus, BooleanEnum.TRUE.getType())
                .gt(ResidentCheckInInfoEntity::getCheckInTime, startOfDay)
                .in(ResidentCheckInInfoEntity::getMedicineName, param.getMedicineNameList())
        );
        if (CollUtil.isNotEmpty(checkInDataList)) {
            throw new BusinessException(BizErrorCode.NFC_CHECK_IN_REPEAT_TAKE);
        }

        boolean saveRes = residentCheckInInfoService.saveBatch(buildCheckInDataList(param, residentInfo, caregiverInfo));
        if (!saveRes) {
            log.info("nfc manager, fail to check in, param:{}", param);
            return false;
        }
        return true;
    }


    private List<ResidentCheckInInfoEntity> buildCheckInDataList(NfcCheckInParam param, ResidentInfoEntity residentInfo, CaregiverInfoEntity caregiverInfo) {
        List<String> medicineNameList = param.getMedicineNameList();
        if (CollUtil.isEmpty(medicineNameList)) {
            return Collections.emptyList();
        }
        return medicineNameList.stream().map(medicineName -> buildCheckInDataEntity(medicineName, param, residentInfo, caregiverInfo)).toList();
    }

    private ResidentCheckInInfoEntity buildCheckInDataEntity(String medicineName, NfcCheckInParam param, ResidentInfoEntity residentInfo, CaregiverInfoEntity caregiverInfo) {
        ResidentCheckInInfoEntity entity = new ResidentCheckInInfoEntity();

        entity.setMedicineName(medicineName);
        entity.setCheckInTime(LocalDateTime.now());
        entity.setTakeTimeType(param.getTakeTimeType());
        entity.setMedicineTakeStatus(param.getTakeStatus());
        entity.setInstitutionId(residentInfo.getInstitutionId());

        entity.setResidentId(residentInfo.getId());
        entity.setLocationInfo(residentInfo.getShowLocationInfo());
        entity.setResidentName(residentInfo.getResidentName());
        entity.setResidentNamePinyin(residentInfo.getResidentNamePinyin());

        entity.setCaregiverId(caregiverInfo.getId());
        entity.setCaregiverDisplayId(caregiverInfo.getDisplayId());

        return entity;
    }
}
