package com.nutrimedcare.backend.terraweb.manager.medicine.impl;

import cn.hutool.core.collection.CollUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.nutrimedcare.backend.terraweb.dao.medicine.MedicineInfoEntity;
import com.nutrimedcare.backend.terraweb.dto.third.MedicineImageRecognitionResp;
import com.nutrimedcare.backend.terraweb.enums.medicine.MedicineTypeEnum;
import com.nutrimedcare.backend.terraweb.manager.medicine.MedicineManager;
import com.nutrimedcare.backend.terraweb.param.medicine.MedicineImageRecognitionParam;
import com.nutrimedcare.backend.terraweb.service.MedicineInfoService;
import com.nutrimedcare.backend.terraweb.third.ThirdService;
import com.nutrimedcare.backend.terraweb.vo.medicine.MedicineHospitalListVO;
import com.nutrimedcare.backend.terraweb.vo.medicine.MedicineImageRecognitionVO;
import com.nutrimedcare.backend.terraweb.vo.medicine.MedicineInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class MedicineManagerImpl implements MedicineManager {

    private static final List<String> HOSPITAL_LIST = Arrays.asList(
            "-", "网购", "药房", "上海天佑医院", "南京东路街道社区卫生服务中心", "外滩街道社区卫生服务中心", "豫园街道社区卫生服务中心", "淮海中路街道社区卫生服务中心", "打浦桥街道社区卫生服务中心", "老西门街道社区卫生服务中心", "小东门街道社区卫生服务中心", "五里桥街道社区卫生服务中心", "半淞园路街道社区卫生服务中心", "瑞金二路街道社区卫生服务中心", "漕河泾街道社区卫生服务中心", "长桥街道社区卫生服务中心", "天平街道社区卫生服务中心", "湖南街道社区卫生服务中心", "凌云街道社区卫生服务中心", "徐家汇街道社区卫生服务中心", "虹梅街道社区卫生服务中心", "田林街道社区卫生服务中心", "龙华街道社区卫生服务中心", "枫林街道社区卫生服务中心", "斜土街道社区卫生服务中心", "华泾镇社区卫生服务中心", "康健街道社区卫生服务中心", "石门二路街道社区卫生服务中心", "共和新路街道社区卫生服务中心", "彭浦镇第二社区卫生服务中心", "临汾路街道社区卫生服务中心", "北站街道社区卫生服务中心", "彭浦新村街道社区卫生服务中心", "天目西路街道社区卫生服务中心", "芷江西路街道社区卫生服务中心", "彭浦镇社区卫生服务中心", "大宁路街道社区卫生服务中心", "曹家渡街道社区卫生服务中心", "宝山路街道社区卫生服务中心", "江宁路街道社区卫生服务中心", "南京西路街道社区卫生服务中心", "静安寺街道社区卫生服务中心", "华阳街道社区卫生服务中心", "江苏街道社区卫生服务中心", "新华街道社区卫生服务中心", "周家桥街道社区卫生服务中心", "天山路街道社区卫生服务中心", "仙霞街道社区卫生服务中心", "虹桥街道社区卫生服务中心", "程家桥街道社区卫生服务中心", "北新泾街道社区卫生服务中心", "新泾镇社区卫生服务中心", "曹杨街道社区卫生服务中心", "甘泉街道社区卫生服务中心", "长风街道长风社区卫生服务中心", "长风街道白玉社区卫生服务中心", "长寿街道社区卫生服务中心", "宜川街道社区卫生服务中心", "石泉街道社区卫生服务中心", "真如镇街道社区卫生服务中心", "长征镇社区卫生服务中心", "桃浦镇社区卫生服务中心", "桃浦镇第二社区卫生服务中心", "万里街道社区卫生服务中心", "北外滩街道社区卫生服务中心", "嘉兴路街道社区卫生服务中心", "四川北路街道社区卫生服务中心", "欧阳路街道社区卫生服务中心", "曲阳路街道社区卫生服务中心", "广中路街道社区卫生服务中心", "江湾镇街道社区卫生服务中心", "凉城新村街道社区卫生服务中心", "长白社区卫生服务中心", "大桥社区卫生服务中心", "延吉社区卫生服务中心", "殷行社区卫生服务中心", "平凉社区卫生服务中心", "五角场社区卫生服务中心", "控江社区卫生服务中心", "定海社区卫生服务中心", "新江湾城社区卫生服务中心", "江浦社区卫生服务中心", "四平社区卫生服务中心", "长海社区卫生服务中心", "江川社区卫生服务中心", "古美社区卫生服务中心", "新虹社区卫生服务中心", "华漕社区卫生服务中心", "虹桥社区卫生服务中心", "七宝社区卫生服务中心", "梅陇社区卫生服务中心", "莘庄社区卫生服务中心", "颛桥社区卫生服务中心", "马桥社区卫生服务中心", "吴泾社区卫生服务中心", "浦江社区卫生服务中心", "浦锦社区卫生服务中心", "申鑫社区卫生服务中心", "月浦镇盛桥社区卫生服务中心", "吴淞街道社区卫生服务中心", "张庙街道泗塘社区卫生服务中心", "大场镇祁连社区卫生服务中心", "顾村镇菊泉新城社区卫生服务中心", "友谊街道社区卫生服务中心", "罗店镇第三社区卫生服务中心", "顾村镇社区卫生服务中心", "高境镇社区卫生服务中心", "罗店镇社区卫生服务中心", "月浦镇社区卫生服务中心", "张庙街道长江路社区卫生服务中心", "大场镇第三社区卫生服务中心", "罗泾镇社区卫生服务中心", "大场镇大场社区卫生服务中心", "庙行镇社区卫生服务中心", "杨行镇社区卫生服务中心", "淞南镇社区卫生服务中心", "真新社区卫生服务中心", "华亭镇社区卫生服务中心", "江桥镇社区卫生服务中心（金沙新城社区卫生服务中心）", "马陆镇社区卫生服务中心", "嘉定镇街道社区卫生服务中心", "安亭镇黄渡社区卫生服务中心", "安亭镇社区卫生服务中心", "菊园新区社区卫生服务中心", "外冈镇社区卫生服务中心", "嘉定区迎园医院", "（新成路街道社区卫生服务中心）", "嘉定工业区社区卫生服务中心", "徐行镇社区卫生服务中心", "南翔镇社区卫生服务中心", "凌桥社区卫生服务中心", "万祥社区卫生服务中心", "南汇新城镇社区卫生服务中心", "金桥社区卫生服务中心", "浦兴社区卫生服务中心", "三林社区卫生服务中心", "洋泾社区卫生服务中心", "陆家嘴社区卫生服务中心", "大团社区卫生服务中心", "周家渡社区卫生服务中心", "川沙华夏社区卫生服务中心", "上钢社区卫生服务中心", "高行社区卫生服务中心", "迎博社区卫生服务中心", "惠南社区卫生服务中心", "新场社区卫生服务中心", "孙桥社区卫生服务中心", "东明社区卫生服务中心", "北蔡社区卫生服务中心", "合庆社区卫生服务中心", "机场社区卫生服务中心", "六灶社区卫生服务中心", "高东社区卫生服务中心", "王港社区卫生服务中心", "三林康德社区卫生服务中心", "航头鹤沙社区卫生服务中心", "老港社区卫生服务中心", "金杨社区卫生服务中心", "潍坊社区卫生服务中心", "塘桥社区卫生服务中心", "曹路社区卫生服务中心", "高桥社区卫生服务中心", "康桥社区卫生服务中心", "川沙社区卫生服务中心", "泥城社区卫生服务中心", "宣桥社区卫生服务中心", "张江社区卫生服务中心", "周浦社区卫生服务中心", "沪东社区卫生服务中心", "南码头社区卫生服务中心", "航头社区卫生服务中心", "江镇社区卫生服务中心", "书院社区卫生服务中心", "唐镇社区卫生服务中心", "祝桥社区卫生服务中心", "花木社区卫生服务中心", "岳阳街道社区卫生服务中心", "永丰街道社区卫生服务中心", "中山街道社区卫生服务中心", "方松街道社区卫生服务中心", "车墩镇社区卫生服务中心", "新桥镇社区卫生服务中心", "洞泾镇社区卫生服务中心", "九亭镇社区卫生服务中心", "泗泾镇社区卫生服务中心", "佘山镇社区卫生服务中心", "小昆山镇社区卫生服务中心", "石湖荡镇社区卫生服务中心", "新浜镇社区卫生服务中心", "泖港镇社区卫生服务中心", "叶榭镇社区卫生服务中心", "佘山镇第二社区卫生服务中心", "广富林街道社区卫生服务中心", "九里亭街道社区卫生服务中心", "朱泾社区卫生服务中心", "廊下镇社区卫生服务中心", "石化社区卫生服务中心", "吕巷镇社区卫生服务中心", "金山卫镇社区卫生服务中心", "漕泾镇社区卫生服务中心", "山阳镇社区卫生服务中心", "亭林镇社区卫生服务中心", "张堰镇社区卫生服务中心", "枫泾镇社区卫生服务中心", "上海湾区高新技术产业开发区社区卫生服务中心", "赵巷镇社区卫生服务中心", "徐泾镇社区卫生服务中心", "华新镇社区卫生服务中心", "金泽镇社区卫生服务中心", "练塘镇社区卫生服务中心", "朱家角镇社区卫生服务中心", "白鹤镇社区卫生服务中心", "重固镇社区卫生服务中心", "盈浦街道社区卫生服务中心", "夏阳街道（新城一站）社区卫生服务中心", "香花桥街道社区卫生服务中心", "徐泾北大居社区卫生服务中心", "南桥镇社区卫生服务中心", "四团镇社区卫生服务中心", "金海社区卫生服务中心", "青村镇社区卫生服务中心", "四团镇平安社区卫生服务中心", "柘林镇社区卫生服务中心", "奉浦街道社区卫生服务中心", "西渡街道社区卫生服务中心", "奉城镇头桥社区卫生服务中心", "金汇镇社区卫生服务中心", "庄行镇邬桥社区卫生服务中心", "海湾镇社区卫生服务中心", "南桥镇光明社区卫生服务中心", "金汇镇泰日社区卫生服务中心", "奉城镇社区卫生服务中心", "庄行镇社区卫生服务中心", "堡镇社区卫生服务中心", "三星镇社区卫生服务中心", "建设镇社区卫生服务中心", "港西镇社区卫生服务中心", "东平镇社区卫生服务中心", "港沿镇社区卫生服务中心", "庙镇社区卫生服务中心", "陈家镇社区卫生服务中心", "新村乡社区卫生服务中心", "绿华镇社区卫生服务中心", "新河镇社区卫生服务中心", "中兴镇社区卫生服务中心", "新海镇社区卫生服务中心", "竖新镇社区卫生服务中心", "城桥镇社区卫生服务中心", "长兴镇社区卫生服务中心", "横沙乡社区卫生服务中心", "向化镇社区卫生服务中心", "复旦大学附属儿科医院", "复旦大学附属妇产科医院", "复旦大学附属妇产科医院长三角一体化示范区青浦分院", "复旦大学附属华山医院", "复旦大学附属金山医院", "复旦大学附属眼耳鼻喉科医院", "复旦大学附属中山医院", "复旦大学附属中山医院青浦分院", "复旦大学附属肿瘤医院", "海军军医大学第二附属医院", "海军军医大学第三附属医院", "海军军医大学第一附属医院", "华东医院", "民航上海医院", "上海电力医院", "上海国际旅行卫生保健中心、上海海关口岸门诊部", "上海国际旅行医疗保健门诊部", "上海航道医院", "上海虹桥国际机场医疗急救中心", "上海化学工业区医疗中心", "上海建工医院", "上海健康医学院附属崇明医院", "上海交通大学医学院附属第九人民医院", "上海交通大学医学院附属第九人民医院黄浦分院", "上海交通大学医学院附属仁济医院", "上海交通大学医学院附属瑞金医院", "上海交通大学医学院附属瑞金医院卢湾分院", "上海交通大学医学院附属上海儿童医学中心", "上海交通大学医学院附属新华医院", "上海交通大学医学院附属新华医院长兴分院", "上海市宝山区大场医院", "上海市宝山区妇幼保健所", "上海市宝山区精神卫生中心", "上海市宝山区罗店医院", "上海市宝山区罗店医院驻宝山区看守所（拘留所）门诊部", "上海市宝山区仁和医院", "上海市宝山区吴淞中心医院", "上海市宝山区医疗急救中心", "上海市宝山区中西医结合医院", "上海市保健医疗中心", "上海市崇明区传染病医院", "上海市崇明区第三人民医院", "上海市崇明区妇幼保健所", "上海市崇明区精神卫生中心", "上海市崇明区康乐医院", "上海市崇明区医疗急救中心", "上海市第八人民医院", "上海市第二康复医院", "上海市第六人民医院", "上海市第六人民医院金山分院", "上海市第七人民医院", "上海市第三康复医院", "上海市第十人民医院", "上海市第十人民医院崇明分院", "上海市第四康复医院", "上海市第四人民医院", "上海市第五康复医院", "上海市第五人民医院", "上海市第一妇婴保健院", "上海市第一康复医院", "上海市第一人民医院", "上海市东方医院", "上海市儿童医院", "上海市肺科医院", "上海市奉贤区奉城医院", "上海市奉贤区妇幼保健所", "上海市奉贤区古华医院", "上海市奉贤区精神卫生中心", "上海市奉贤区皮肤病防治所", "上海市奉贤区牙病防治所", "上海市奉贤区医疗急救中心", "上海市奉贤区中心医院", "上海市奉贤区中医医院", "上海市妇幼保健中心", "上海市工人疗养院", "上海市公共卫生临床中心", "上海市公惠医院", "上海市光华中西医结合医院", "上海市虹口区妇幼保健所", "上海市虹口区江湾医院", "上海市虹口区精神卫生中心", "上海市虹口区牙病防治所", "上海市化工职业病防治院", "上海市黄浦区第二牙病防治所", "上海市黄浦区妇幼保健所", "上海市黄浦区精神卫生中心", "上海市黄浦区香山中医医院", "上海市黄浦区牙病防治所", "上海市黄浦区中西医结合医院", "上海市嘉定区安亭医院", "上海市嘉定区妇幼保健院", "上海市嘉定区江桥医院", "上海市嘉定区精神卫生中心", "上海市嘉定区南翔医院", "上海市嘉定区牙病防治所", "上海市嘉定区医疗急救中心", "上海市嘉定区中心医院", "上海市嘉定区中医医院", "上海市金山区妇幼保健所", "上海市金山区精神卫生中心", "上海市金山区亭林医院", "上海市金山区牙病防治所", "上海市金山区医疗急救中心", "上海市金山区中西医结合医院", "上海市精神卫生中心", "上海市静安区北站医院", "上海市静安区妇幼保健所", "上海市静安区精神卫生中心", "上海市静安区市北医院", "上海市静安区牙病防治所", "上海市静安区闸北中心医院", "上海市静安区中心医院", "上海市静安区中医医院", "上海市口腔医院", "上海市老年医学中心", "上海市临床检验中心", "上海市民政第三精神卫生中心", "上海市民政第一精神卫生中心", "上海市闵行区妇幼保健院", "上海市闵行区精神卫生中心", "上海市闵行区浦江医院", "上海市闵行区牙病防治所", "上海市闵行区医疗急救中心", "上海市闵行区中西医结合医院", "上海市闵行区中心医院", "上海市闵行区肿瘤医院", "上海市皮肤病医院", "上海市浦东新区公利医院", "上海市浦东新区人民医院", "上海市浦东新区周浦医院", "上海市浦东医院", "上海市普陀区妇婴保健院", "上海市普陀区精神卫生中心", "上海市普陀区利群医院", "上海市普陀区人民医院", "上海市普陀区眼病牙病防治所", "上海市普陀区中心医院", "上海市普陀区中医医院", "上海市气功研究所医疗门诊部", "上海市强制医疗所", "上海市青浦区妇幼保健所", "上海市青浦区精神卫生中心", "上海市青浦区医疗急救中心", "上海市青浦区中医医院", "上海市青浦区朱家角人民医院", "上海市瑞金康复医院", "上海市松江区方塔中医医院", "上海市松江区妇幼保健院", "上海市松江区精神卫生中心", "上海市松江区九亭医院", "上海市松江区口腔病防治所", "上海市松江区泗泾医院", "上海市松江区医疗急救中心", "上海市松江区中心医院", "上海市同济医院", "上海市同仁医院", "上海市胸科医院", "上海市徐汇区大华医院", "上海市徐汇区妇幼保健所", "上海市徐汇区精神卫生中心", "上海市徐汇区牙病防治所", "上海市徐汇区中心医院", "上海市眼病防治中心", "上海市杨浦区妇幼保健院（所）", "上海市杨浦区精神卫生中心", "上海市杨浦区控江医院", "上海市杨浦区市东医院", "上海市杨浦区牙病防治所", "上海市杨浦区中心医院", "上海市杨浦区中医医院", "上海市养志康复医院", "上海市医疗急救中心", "上海市预防医学研究院门诊部", "上海市长宁区妇幼保健院", "上海市长宁区精神卫生中心", "上海市长宁区天山中医医院", "上海市针灸经络研究所医疗门诊部", "上海市质子重离子医院", "上海市中西医结合医院", "上海市中医文献馆中医门诊部", "上海市中医医院", "上海四一一医院", "上海文艺医院", "上海邮电医院", "上海中冶医院", "上海中医药大学附属龙华医院", "上海中医药大学附属曙光医院", "上海中医药大学附属岳阳中西医结合医院", "同济大学附属口腔医院", "同济大学附属同济医院分院", "中国福利会国际和平妇幼保健院", "中国人民解放军海军第九〇五医院", "中国人民解放军海军特色医学中心", "中国人民武装警察部队上海市总队医院"
    );

    @Autowired
    private ObjectMapper mapper;

    @Autowired
    private ThirdService thirdService;

    @Autowired
    private MedicineInfoService medicineInfoService;

    @Override
    public MedicineHospitalListVO hospitalList(Long institutionId, String hospitalName) {
        List<String> filterList = HOSPITAL_LIST.stream().filter(name -> {
            if (StringUtils.isBlank(hospitalName)) {
                return true;
            }
            return name.contains(hospitalName);
        }).toList();

        return MedicineHospitalListVO.builder().hospitalList(filterList).build();
    }

    @Override
    public MedicineImageRecognitionVO imageRecognition(MedicineImageRecognitionParam param) {
        if (param == null || CollUtil.isEmpty(param.getImages())) {
            return null;
        }

        List<MedicineImageRecognitionResp> respList = getRespList(param.getImages(), param.getMedicineType());
        if (CollUtil.isEmpty(respList)) {
            return MedicineImageRecognitionVO.builder().medicineInfoList(Collections.emptyList()).build();
        }

        Set<String> medicineNames = respList.stream().map(MedicineImageRecognitionResp::getName).collect(Collectors.toSet());
        Map<String, MedicineInfoEntity> lastSameNameMedicineMap = findLastSameNameMedicine(medicineNames, param.getResidentId());

        List<MedicineInfoVO> voList = respList.stream()
                .map(resp -> MedicineInfoVO.convert2VO(resp, lastSameNameMedicineMap.get(resp.getName())))
                .toList();

        return MedicineImageRecognitionVO.builder().medicineInfoList(voList).build();
    }

    private List<MedicineImageRecognitionResp> getRespList(List<String> images, Integer medicineType) {
        if (CollUtil.isEmpty(images)) {
            return Collections.emptyList();
        }

        // 如果是代配药并且只上传了一张图片，则使用多药品识别
        if (MedicineTypeEnum.DISPENSING_OF_MEDICINES.getType().equals(medicineType) && images.size() == 1) {
            return thirdService.multiMedicineImageRecognition(images.get(0));
        }

        List<MedicineImageRecognitionResp> respList = thirdService.medicineImageRecognition(images);
        // 图像服务器不返回多图片的解析结果，所以需要手动合并
        MedicineImageRecognitionResp mergedResp = mergeRespList(respList);

        return Collections.singletonList(mergedResp);
    }

    private Map<String, MedicineInfoEntity> findLastSameNameMedicine(Set<String> medicineNames, Long residentId) {
        if (CollUtil.isEmpty(medicineNames) || residentId == null) {
            return Collections.emptyMap();
        }
        List<MedicineInfoEntity> sampleNameMedicineList = medicineInfoService.findLastSameNameMedicine(residentId, new ArrayList<>(medicineNames), null);

        return sampleNameMedicineList.stream().collect(Collectors.toMap(MedicineInfoEntity::getMedicineName, Function.identity()));
    }

    private MedicineImageRecognitionResp mergeRespList(List<MedicineImageRecognitionResp> respList) {
        if (CollUtil.isEmpty(respList)) {
            return null;
        }
        ObjectNode mergedNode = mapper.createObjectNode();
        for (MedicineImageRecognitionResp resp : respList) {
            JsonNode jsonNode = mapper.valueToTree(resp);
            jsonNode.fields().forEachRemaining(entry -> {
                if (!entry.getValue().isNull() && StringUtils.isNotBlank(entry.getValue().asText()) && !mergedNode.hasNonNull(entry.getKey())) {
                    mergedNode.set(entry.getKey(), entry.getValue());
                }
            });
        }
        return mapper.convertValue(mergedNode, MedicineImageRecognitionResp.class);
    }

}