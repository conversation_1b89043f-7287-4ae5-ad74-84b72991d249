package com.nutrimedcare.backend.terraweb.manager.resident.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nutrimedcare.backend.terraweb.dao.resident.ResidentExtInfoEntity;
import com.nutrimedcare.backend.terraweb.manager.resident.ResidentExtInfoManager;
import com.nutrimedcare.backend.terraweb.param.resident.ResidentExtInfoSaveParam;
import com.nutrimedcare.backend.terraweb.service.resident.ResidentExtInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class ResidentExtInfoManagerImpl implements ResidentExtInfoManager {

    @Autowired
    private ResidentExtInfoService residentExtInfoService;

    @Override
    public boolean save(ResidentExtInfoSaveParam param) {
        if (param == null) {
            log.warn("saveExtInfo param is null");
            return false;
        }

        // 查询是否已存在相同 residentId 和 extType 的记录
        ResidentExtInfoEntity existingEntity = residentExtInfoService.getOne(
                new LambdaQueryWrapper<ResidentExtInfoEntity>()
                        .eq(ResidentExtInfoEntity::getResidentId, param.getResidentId())
                        .eq(ResidentExtInfoEntity::getExtType, param.getExtType())
        );

        ResidentExtInfoEntity entity = new ResidentExtInfoEntity();
        BeanUtils.copyProperties(param, entity);

        boolean result;
        if (existingEntity != null) {
            // 如果存在，则覆盖（更新）
            entity.setId(existingEntity.getId());
            result = residentExtInfoService.updateById(entity);
            log.info("update resident ext info, residentId: {}, extType: {}, result: {}",
                    param.getResidentId(), param.getExtType(), result);
        } else {
            // 如果不存在，则新增
            result = residentExtInfoService.save(entity);
            log.info("save resident ext info, residentId: {}, extType: {}, result: {}",
                    param.getResidentId(), param.getExtType(), result);
        }

        return result;
    }

}