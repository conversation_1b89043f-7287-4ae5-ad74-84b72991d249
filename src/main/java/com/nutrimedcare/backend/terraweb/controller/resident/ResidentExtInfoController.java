package com.nutrimedcare.backend.terraweb.controller.resident;

import com.nutrimedcare.backend.terraweb.common.Result;
import com.nutrimedcare.backend.terraweb.manager.resident.ResidentExtInfoManager;
import com.nutrimedcare.backend.terraweb.param.resident.ResidentExtInfoSaveParam;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Validated
@RestController
@RequestMapping("/resident/ext")
public class ResidentExtInfoController {

    @Autowired
    private ResidentExtInfoManager residentExtInfoManager;

    @PostMapping("/save")
    public Result<Boolean> save(@Valid @RequestBody ResidentExtInfoSaveParam param) {
        return Result.success(residentExtInfoManager.save(param));
    }

} 