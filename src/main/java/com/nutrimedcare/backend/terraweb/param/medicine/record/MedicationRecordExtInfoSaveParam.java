package com.nutrimedcare.backend.terraweb.param.medicine.record;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class MedicationRecordExtInfoSaveParam {

    @NotNull(message = "服药单记录 id 不能为空")
    private Long recordId;

    @NotNull
    private Integer usageIndex;

    /**
     * @see com.nutrimedcare.backend.terraweb.enums.medicine.record.MedicationRecordExtInfoTypeEnum
     */
    @NotNull
    private Integer extType;

    @NotEmpty
    private String extValue;

}


