package com.nutrimedcare.backend.terraweb.param.resident;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class ResidentSaveParam {

    private Long id;

    @NotNull(message = "机构ID不能为空")
    private Long institutionId;

    @NotBlank(message = "姓名不能为空")
    private String residentName;

    /**
     * @see com.nutrimedcare.backend.terraweb.enums.ResidentGenderEnum
     */
    @NotNull(message = "性别不能为空")
    @Range(min = 1, max = 3, message = "无法匹配性别")
    private Integer gender;

    @NotNull(message = "出生日期不能为空")
    private LocalDateTime birthdate;

    private String buildingNum;

    @NotBlank(message = "室号不能为空")
    private String roomNum;

    private String bedNum;

    public void trimFields() {
        if (residentName != null) {
            residentName = residentName.trim();
        }
        if (buildingNum != null) {
            buildingNum = buildingNum.trim();
        }
        if (roomNum != null) {
            roomNum = roomNum.trim();
        }
        if (bedNum != null) {
            bedNum = bedNum.trim();
        }
    }

} 