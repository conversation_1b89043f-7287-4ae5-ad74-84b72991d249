package com.nutrimedcare.backend.terraweb.util;

import com.nutrimedcare.backend.terraweb.enums.medicine.record.MedicationRecordTimeTypeEnum;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

/**
 * <AUTHOR>
 */
class MedicineRecordTimeUtilTest {

    @Test
    void testCalMedicationTimeWithDefaultConfig() {
        // 测试早餐前时间段 [6:00, 7:00)
        LocalDateTime morningTime = LocalDateTime.of(2024, 1, 1, 6, 30);
        Integer result = MedicineRecordTimeUtil.calMedicationTime(morningTime, null);
        assertEquals(MedicationRecordTimeTypeEnum.BEFORE_BREAKFAST.getType(), result);

        // 测试早餐后时间段 [7:00, 8:00)
        LocalDateTime afterBreakfastTime = LocalDateTime.of(2024, 1, 1, 7, 30);
        result = MedicineRecordTimeUtil.calMedicationTime(afterBreakfastTime, null);
        assertEquals(MedicationRecordTimeTypeEnum.AFTER_BREAKFAST.getType(), result);

        // 测试午餐前时间段 [10:00, 11:00)
        LocalDateTime beforeLunchTime = LocalDateTime.of(2024, 1, 1, 10, 30);
        result = MedicineRecordTimeUtil.calMedicationTime(beforeLunchTime, null);
        assertEquals(MedicationRecordTimeTypeEnum.BEFORE_LUNCH.getType(), result);

        // 测试睡前时间段 [19:30, 20:30)
        LocalDateTime beforeSleepTime = LocalDateTime.of(2024, 1, 1, 20, 0);
        result = MedicineRecordTimeUtil.calMedicationTime(beforeSleepTime, null);
        assertEquals(MedicationRecordTimeTypeEnum.BEFORE_SLEEP.getType(), result);
    }

    @Test
    void testCalMedicationTimeWithCustomConfig() {
        // 自定义配置
        Map<Integer, List<String>> customConfig = new HashMap<>();
        customConfig.put(MedicationRecordTimeTypeEnum.BEFORE_BREAKFAST.getType(),
                Arrays.asList("08:00", "09:00"));
        customConfig.put(MedicationRecordTimeTypeEnum.AFTER_BREAKFAST.getType(),
                Arrays.asList("09:00", "10:00"));

        // 测试自定义早餐前时间段 [8:00, 9:00)
        LocalDateTime customTime = LocalDateTime.of(2024, 1, 1, 8, 30);
        Integer result = MedicineRecordTimeUtil.calMedicationTime(customTime, customConfig);
        assertEquals(MedicationRecordTimeTypeEnum.BEFORE_BREAKFAST.getType(), result);

        // 测试自定义早餐后时间段 [9:00, 10:00)
        LocalDateTime customTime2 = LocalDateTime.of(2024, 1, 1, 9, 30);
        result = MedicineRecordTimeUtil.calMedicationTime(customTime2, customConfig);
        assertEquals(MedicationRecordTimeTypeEnum.AFTER_BREAKFAST.getType(), result);
    }

    @Test
    void testCalMedicationTimeWithNoMatch() {
        // 测试没有匹配的时间段
        LocalDateTime noMatchTime = LocalDateTime.of(2024, 1, 1, 2, 0);
        Integer result = MedicineRecordTimeUtil.calMedicationTime(noMatchTime, null);
        assertNull(result);
    }

    @Test
    void testCalMedicationTimeWithNullInput() {
        // 测试空输入
        Integer result = MedicineRecordTimeUtil.calMedicationTime(null, null);
        assertNull(result);
    }

    @Test
    void testCalMedicationTimeWithEmptyConfig() {
        // 测试空配置，应该使用默认配置
        Map<Integer, List<String>> emptyConfig = new HashMap<>();
        LocalDateTime morningTime = LocalDateTime.of(2024, 1, 1, 6, 30);
        Integer result = MedicineRecordTimeUtil.calMedicationTime(morningTime, emptyConfig);
        assertEquals(MedicationRecordTimeTypeEnum.BEFORE_BREAKFAST.getType(), result);
    }

    @Test
    void testCalMedicationTimeWithInvalidTimeFormat() {
        // 测试无效的时间格式
        Map<Integer, List<String>> invalidConfig = new HashMap<>();
        invalidConfig.put(MedicationRecordTimeTypeEnum.BEFORE_BREAKFAST.getType(),
                Arrays.asList("invalid", "time"));

        LocalDateTime testTime = LocalDateTime.of(2024, 1, 1, 6, 30);
        Integer result = MedicineRecordTimeUtil.calMedicationTime(testTime, invalidConfig);
        assertNull(result);
    }

    @Test
    void testCalMedicationTimeWithIncompleteTimeRange() {
        // 测试不完整的时间范围
        Map<Integer, List<String>> incompleteConfig = new HashMap<>();
        incompleteConfig.put(MedicationRecordTimeTypeEnum.BEFORE_BREAKFAST.getType(),
                Arrays.asList("06:00")); // 只有一个时间

        LocalDateTime testTime = LocalDateTime.of(2024, 1, 1, 6, 30);
        Integer result = MedicineRecordTimeUtil.calMedicationTime(testTime, incompleteConfig);
        assertNull(result);
    }

    @Test
    void testBoundaryTimes() {
        // 测试边界时间
        // 6:00 应该匹配早餐前 [6:00, 7:00)
        LocalDateTime boundaryStart = LocalDateTime.of(2024, 1, 1, 6, 0);
        Integer result = MedicineRecordTimeUtil.calMedicationTime(boundaryStart, null);
        assertEquals(MedicationRecordTimeTypeEnum.BEFORE_BREAKFAST.getType(), result);

        // 7:00 应该匹配早餐后 [7:00, 8:00)，不匹配早餐前
        LocalDateTime boundaryEnd = LocalDateTime.of(2024, 1, 1, 7, 0);
        result = MedicineRecordTimeUtil.calMedicationTime(boundaryEnd, null);
        assertEquals(MedicationRecordTimeTypeEnum.AFTER_BREAKFAST.getType(), result);
    }
}
